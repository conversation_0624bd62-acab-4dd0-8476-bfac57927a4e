package com.mycom.system.common.configure;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * security 允许访问配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "security.matchers")
public class SecurityAntMatchersConfig {
    List<String> permitAll;
    List<String> anonymous;
    List<String> imagesAll;
}
