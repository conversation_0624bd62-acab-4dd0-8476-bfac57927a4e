package com.ruoyi.web.controller.sysapp;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.AppUser;
import com.ruoyi.common.core.domain.model.LoginAppBody;
import com.ruoyi.framework.web.service.AppLoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api("APP登录管理")
@RestController
@RequestMapping("/app")
public class AppLoginController extends BaseController {

    @Resource
    private AppLoginService appLoginService;

    @ApiOperation("登录")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginAppBody loginAppBody) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = appLoginService.login(loginAppBody.getUsername(), loginAppBody.getPassword());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    @ApiOperation("根据token获取用户信息")
    @GetMapping("/userInfo")
    @Anonymous
    public AjaxResult userInfo() {
        AppUser appUser = getLoginAppUser().getAppUser();
        AjaxResult ajax = AjaxResult.success();
        ajax.put("appUser", appUser);
        return ajax;
    }
}
