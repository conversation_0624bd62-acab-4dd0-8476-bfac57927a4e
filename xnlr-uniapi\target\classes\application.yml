server:
  port: 8080
  servlet:
    context-path: /
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #  test-mybatis
    url: **************************************************************************************************************************************************************************************************************
    username: root
    password: 1234
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    db-config:
      # 逻辑删除标志
      logic-not-delete-value: 1   # 未删除的记录值
      logic-delete-value: 0       # 已删除的记录值

# 不需要权限的接口
security:
  matchers:
    permitAll:
      - /login/wechat
      - /user/list

# 小程序微信API
applet-api:
  # 小程序基础参数
  url: https://api.weixin.qq.com
  #appid: wxf5a8941d0d5617c1
  appid: wx000992cef6ab3666
  secret: 4915e414df2e4b393415410cee75605a
  grantType: authorization_code
  # 微信支付参数
  payUrl: https://api.mch.weixin.qq.com
  timeout: 5
