package com.mycom.system.service.impl;

import com.mycom.system.domain.GiftEntity;
import com.mycom.system.mapper.GiftMapper;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;
import java.util.List;

/**
 * (Gift)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-25 10:29:21
 */
@Service
public class GiftService {
    @Resource
    private GiftMapper giftMapper;

    /**
     * 通过条件筛选，分页查询
     *
     * @param giftEntity 查询条件
     * @return 多条数据
     */
    public PageInfo<GiftEntity> getListPage(GiftEntity giftEntity) {
        PageHelper.startPage(giftEntity.getPageNum(), giftEntity.
                getPageSize());
        List<GiftEntity> list = giftMapper.getList(giftEntity);
        return new PageInfo<>(list);
    }

    /**
     * 通过条件筛选，查询全部
     *
     * @param giftEntity 查询条件
     * @return 多条数据
     */
    public List<GiftEntity> getList(GiftEntity giftEntity) {
        List<GiftEntity> list = giftMapper.getList(giftEntity);
        return list;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    public GiftEntity queryById(Long id) {
        return giftMapper.selectByPrimaryKey(id);
    }

    /**
     * 新增数据
     *
     * @param giftEntity 实例对象
     * @return 实例对象
     */
    public GiftEntity insert(GiftEntity giftEntity) {
        giftMapper.insert(giftEntity);
        return giftEntity;
    }

    /**
     * 修改数据
     *
     * @param giftEntity 实例对象
     * @return 实例对象
     */
    public GiftEntity update(GiftEntity giftEntity) {
        giftMapper.update(giftEntity);
        return queryById(giftEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public boolean deleteById(Long id) {
        return giftMapper.deleteById(id) > 0;
    }
}

