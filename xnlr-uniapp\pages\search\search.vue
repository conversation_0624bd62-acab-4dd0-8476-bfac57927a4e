<template>
	<view class="home-container">
		<!--头部搜索-->
		<view class="home-header-search">
			<view class="home-header-input">
				<image src="@/static/iconfont/search.png" mode="widthFix" class="home-header-input-icon" />
				<input type="text" placeholder="请输入呢称/ID/省份" v-model="searchValue" />
			</view>
			<view class="home-header-btn" @tap="search">
				<view>搜索</view>
			</view>
		</view>
		<!--恋人列表-->
		<view class="home-content">
			<view class="home-content-list" v-for="item in userList" :key="item.userId">
				<view class="home-content-list-item">
					<view class="avatar-container">
						<image :src="item.avatar" mode="widthFix" class="home-content-list-item-avatar" />
						<view class="home-content-list-item-content-level" :class="getLevelClass(item)">
							{{ getLevelText(item) }}
						</view>
					</view>
					<view class="home-content-list-item-content">
						<view class="home-content-list-item-content-detail">
							<view class="home-content-list-item-content-nickname">{{ item.nickName }}</view>
							<view class="home-content-list-item-content-point"></view>
							<view class="home-content-list-item-content-isOnline">{{ item.isOnline == true ? '在线' : '离线' }}
							</view>
							<view class="home-content-list-item-content-province">{{ item.province }}</view>
						</view>
						<view class="home-content-list-item-content-sex-box">
							<image
								:src="item.sex === 1 ? '../../static/iconfont/boy.png' : '../../static/iconfont/girl.png'"
								mode="widthFix" class="home-content-list-item-content-sex-icon" />
							<view class="home-content-list-item-content-sex"
								:class="{ 'boy': item.sex === 1, 'girl': item.sex === 0 }">
								{{ item.age }}
							</view>
						</view>
						<view class="home-content-list-item-content-tags">
							<view v-for="(tag, tagIndex) in item.tags.split(',')" :key="tagIndex" class="tag-item">
								{{ tag }}
							</view>
						</view>
						<view class="home-content-list-item-content-voice">30s</view>
					</view>
				</view>
			</view>
			<!-- 加载更多提示 -->
			<view class="loading-more" v-if="hasMore">
				{{ isLoading ? '加载中...' : '下拉加载更多' }}
			</view>
			<view class="no-more" v-else>没有更多数据了</view>
		</view>

	</view>
</template>
<script setup>
import { ref } from 'vue'
import { getUserList } from '../../api/user.js'
import { onLoad } from '@dcloudio/uni-app'
import { onReachBottom } from '@dcloudio/uni-app'

const searchValue = ref('')
const userList = ref([])

// 搜索方法
const search = () => {
	getUserListApp()
}

// 获取等级对应的样式类
const getLevelClass = (item) => {
	const levelMap = {
		'普通': 'level-normal',
		'金牌': 'level-gold',
		'镇店': 'level-shop',
		'男女神': 'level-god'
	}
	return levelMap[item.level] || 'level-normal'
}

// 获取等级显示文本
const getLevelText = (item) => {
	if (item.level === '男女神') {
		return item.sex === 1 ? '男神' : '女神'
	}
	return item.level
}
// 分页相关变量
const pageNum = ref(1)
const pageSize = ref(5)
const total = ref(0)
const isLoading = ref(false)
const hasMore = ref(true)

// 获取用户列表
const getUserListApp = async (isLoadMore = false) => {
	if (isLoading.value) return
	isLoading.value = true

	try {
		const res = await getUserList({
			searchKey: searchValue.value,
			pageNum: pageNum.value,
			pageSize: pageSize.value
		})

		// 更新总数
		total.value = res.total

		// 判断是否还有更多数据
		hasMore.value = pageNum.value * pageSize.value < total.value

		// 如果是加载更多，则追加数据，否则替换数据
		if (isLoadMore) {
			userList.value = [...userList.value, ...res.list]
		} else {
			userList.value = res.list
		}
	} catch (error) {
		console.error('获取用户列表失败：', error)
		uni.showToast({
			title: '获取数据失败',
			icon: 'none'
		})
	} finally {
		isLoading.value = false
	}
}

// 重置分页数据
const resetPagination = () => {
	pageNum.value = 1
	hasMore.value = true
	userList.value = []
}
// 监听页面滚动到底部
onReachBottom(() => {
	if (hasMore.value && !isLoading.value) {
		pageNum.value++
		getUserListApp(true)
	}
})
// 页面加载时获取参数
onLoad((options) => {
	if (options.searchValue) {
		searchValue.value = options.searchValue
		getUserListApp()
	}
})
</script>

<style lang="scss" scoped>
.home-header-search {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px;
	background-color: #008cff;
	z-index: 100;

	.home-header-input {
		flex: 1;
		padding: 3px;
		background-color: #fff;
		border-radius: 20px;
		display: flex;
		align-items: center;

		image {
			width: 20px;
			height: 20px;
			margin: 0 5px;
		}

		input {
			width: 100%;
			height: 30px;
		}
	}

	.home-header-btn {
		width: 50px;
		height: 30px;
		text-align: center;
		line-height: 30px;
		color: #fff;
		font-weight: 600;
	}
}

.home-content {
	padding: 128rpx 15rpx;
	background-color: #f3f3f3;
	min-height: 100vh;

	.home-content-list {
		.home-content-list-item {
			background-color: #ffffff;
			border-radius: 10px;
			padding: 15px;
			margin-bottom: 10px;
			display: flex;

			.avatar-container {
				position: relative;
				// z-index: 0;

				.home-content-list-item-avatar {
					width: 110px;
					height: 110px;
					border-radius: 95px;
					margin-right: 20px;
				}

				.home-content-list-item-content-level {
					position: absolute;
					right: 40rpx;
					bottom: 10rpx;
					padding: 5rpx 15rpx;
					border-radius: 20rpx;
					font-size: 20rpx;
					color: #fff;
					font-weight: 600;

					&.level-normal {
						background-color: #ff95ca;
					}

					&.level-gold {
						background-color: #ffd700;
					}

					&.level-shop {
						background-color: #ff4500;
					}

					&.level-god {
						background: linear-gradient(to right, #ff69b4, #ff1493);
					}
				}
			}

			.home-content-list-item-content {
				flex: 1;
				display: flex;
				flex-direction: column;

				.home-content-list-item-content-detail {
					display: flex;

					.home-content-list-item-content-nickname {
						font-size: 14px;
						font-weight: 600;
					}

					.home-content-list-item-content-point {
						width: 5px;
						height: 5px;
						margin: 7px 5px 0 auto;
						background-color: #55ff00;
						border-radius: 5px;
					}

					.home-content-list-item-content-isOnline {
						margin-right: 10px;
						font-size: 10px;
						color: #454545;
					}

					.home-content-list-item-content-province {
						font-size: 10px;
						color: #454545;
					}
				}

				.home-content-list-item-content-sex-box {
					display: flex;
					align-items: center;
					margin-top: 6px;

					.home-content-list-item-content-sex-icon {
						width: 10px;
						height: 10px;
						margin: 0 10rpx 0 3px;
					}

					.home-content-list-item-content-sex {
						font-size: 12px;
						font-weight: 500;

						&.girl {
							color: #ff87c3;
						}

						&.boy {
							color: #7bc0f9;
						}
					}
				}

				.home-content-list-item-content-tags {
					display: flex;
					flex-wrap: wrap;
					gap: 30rpx;
					margin: 15rpx 0;

					.tag-item {
						padding: 2rpx 20rpx;
						border-radius: 30rpx;
						font-size: 22rpx;
						color: #ff87c3;
						border: 1rpx solid #ff87c3;
						background-color: #fff;
					}
				}

				.home-content-list-item-content-voice {
					// margin-top: 6rpx;
					color: #fff;
					font-weight: 600;
					font-size: 26rpx;
					text-align: center;
					width: 120rpx;
					padding: 6rpx 15rpx;
					margin-top: 8rpx;
					background-color: #5be4ff;
					border-radius: 35rpx;
				}
			}
		}
	}

	.loading-more,
	.no-more {
		text-align: center;
		padding: 30rpx 0;
		font-size: 24rpx;
		color: #999;
	}

	.no-more {
		color: #ccc;
	}
}
</style>
