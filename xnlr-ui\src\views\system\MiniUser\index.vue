<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#409EFF"><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ totalStaff }}</div>
              <div class="stats-label">总店员数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#67C23A"><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ activeStaff }}</div>
              <div class="stats-label">在线店员</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#E6A23C"><Wallet /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ totalEarnings }}</div>
              <div class="stats-label">总收益</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#F56C6C"><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ avgLevel }}</div>
              <div class="stats-label">平均等级</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="search-card mb-4">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        label-width="80px"
      >
        <el-form-item label="用户昵称" prop="nickName">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入用户昵称"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="微信" prop="wechat">
          <el-input
            v-model="queryParams.wechat"
            placeholder="请输入微信"
            clearable
            style="width: 220px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入手机号"
            clearable
            style="width: 220px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="角色" prop="userType">
          <el-select
            v-model="queryParams.userType"
            placeholder="请选择角色"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="dict in sys_user_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select
            v-model="queryParams.sex"
            placeholder="请选择性别"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input
            v-model="queryParams.age"
            placeholder="请输入年龄"
            clearable
            style="width: 150px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input
            v-model="queryParams.province"
            placeholder="请输入省份"
            clearable
            style="width: 180px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-input
            v-model="queryParams.level"
            placeholder="请输入等级"
            clearable
            style="width: 150px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">
            搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:MiniUser:export']"
            style="margin-left: 16px"
          >
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="mb-4 action-card">
      <el-row :gutter="16" justify="space-between" align="middle">
        <el-col :span="12">
          <div class="action-buttons">
            <el-button
              type="success"
              icon="Edit"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:MiniUser:edit']"
              class="action-btn"
            >
              <span>查看详情</span>
            </el-button>
            <el-button
              type="primary"
              icon="Refresh"
              @click="getList"
              class="action-btn"
            >
              <span>刷新列表</span>
            </el-button>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="toolbar-wrapper">
            <right-toolbar
              v-model:showSearch="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 店员列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">店员管理列表</span>
          <span class="card-subtitle">共 {{ total }} 名店员</span>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="MiniUserList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        class="staff-table"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="用户昵称"
          align="center"
          prop="nickName"
          min-width="120"
        />
        <el-table-column label="头像" align="center" prop="avatar" width="80">
          <template #default="scope">
            <img
              :src="scope.row.avatar"
              alt="头像"
              style="width: 50px; height: 50px; border-radius: 50%"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="角色"
          align="center"
          prop="userType"
          min-width="100"
        >
          <template #default="scope">
            <dict-tag :options="sys_user_type" :value="scope.row.userType" />
          </template>
        </el-table-column>
        <el-table-column
          label="余额"
          align="center"
          prop="balance"
          min-width="100"
        />
        <el-table-column
          label="充值总额"
          align="center"
          prop="totalAmount"
          min-width="120"
        />
        <el-table-column
          label="微信"
          align="center"
          prop="wechat"
          min-width="120"
        />
        <el-table-column
          label="手机号"
          align="center"
          prop="phone"
          min-width="130"
        />
        <el-table-column label="性别" align="center" prop="sex" width="80">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
          </template>
        </el-table-column>
        <el-table-column label="年龄" align="center" prop="age" width="80" />
        <el-table-column
          label="省份"
          align="center"
          prop="province"
          min-width="100"
        />
        <el-table-column
          label="标签"
          align="center"
          prop="tags"
          min-width="120"
        />
        <el-table-column
          label="起步价格"
          align="center"
          prop="basePrice"
          min-width="100"
        />
        <el-table-column
          label="签名"
          align="center"
          prop="signature"
          min-width="150"
        />
        <el-table-column label="等级" align="center" prop="level" width="80" />
        <el-table-column
          label="是否启用"
          align="center"
          prop="enabled"
          width="100"
        >
          <template #default="scope">
            <el-tag
              :type="
                scope.row.userType == 3 || scope.row.enabled == 1
                  ? 'success'
                  : 'danger'
              "
              size="small"
            >
              {{
                scope.row.userType == 3
                  ? "上架"
                  : scope.row.enabled == 1
                  ? "上架"
                  : "下架"
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          min-width="150"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <div class="action-buttons-group">
              <el-button
                size="small"
                type="warning"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:MiniUser:edit']"
                class="table-action-btn"
              >
                修改
              </el-button>
              <el-button
                size="small"
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:MiniUser:remove']"
                class="table-action-btn"
                plain
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <div class="pagination-wrapper">
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        class="custom-pagination"
      />
    </div>

    <!-- 添加或修改用户管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="MiniUserRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <!-- <el-form-item label="openid" prop="openid">
          <el-input v-model="form.openid" placeholder="请输入openid" />
        </el-form-item> -->
        <el-form-item label="用户昵称" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <el-input v-model="form.avatar" placeholder="请输入头像" />
        </el-form-item>
        <el-form-item label="余额" prop="balance">
          <el-input v-model="form.balance" placeholder="请输入余额" />
        </el-form-item>
        <el-form-item label="充值总额" prop="totalAmount">
          <el-input v-model="form.totalAmount" placeholder="请输入充值总额" />
        </el-form-item>
        <el-form-item label="微信" prop="wechat">
          <el-input v-model="form.wechat" placeholder="请输入微信" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="角色" prop="userType">
          <el-select v-model="form.userType" placeholder="请选择角色">
            <el-option
              v-for="dict in sys_user_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="form.sex" placeholder="请选择性别">
            <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input v-model="form.age" placeholder="请输入年龄" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="form.province" placeholder="请输入省份" />
        </el-form-item>
        <el-form-item label="标签 " prop="tags">
          <el-input v-model="form.tags" placeholder="请输入标签 " />
        </el-form-item>
        <el-form-item label="是否在线" prop="isOnline">
          <el-input v-model="form.isOnline" placeholder="请输入是否在线" />
        </el-form-item>
        <el-form-item label="起步价格" prop="basePrice">
          <el-input v-model="form.basePrice" placeholder="请输入起步价格" />
        </el-form-item>
        <el-form-item label="签名" prop="signature">
          <el-input v-model="form.signature" placeholder="请输入签名" />
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-input v-model="form.level" placeholder="请输入等级" />
        </el-form-item>
        <el-form-item label="语音" prop="voice">
          <audio :src="form.voice" controls></audio>
        </el-form-item>
        <el-form-item label="语音时长" prop="voiceTime">
          <el-input v-model="form.voiceTime" placeholder="请输入语音时长" />
        </el-form-item>
        <el-form-item label="是否上架" prop="enabled">
          <el-select v-model="form.enabled" placeholder="是否上架">
            <el-option
              v-for="dict in sys_user_enabled"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createdAt">
          <el-date-picker
            clearable
            v-model="form.createdAt"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedAt">
          <el-date-picker
            clearable
            v-model="form.updatedAt"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择更新时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MiniUser">
import {
  listMiniUser,
  getMiniUser,
  delMiniUser,
  addMiniUser,
  updateMiniUser,
} from "@/api/system/MiniUser";
import {
  UserFilled,
  CircleCheck,
  Wallet,
  TrendCharts,
} from "@element-plus/icons-vue";

const { proxy } = getCurrentInstance();
const { sys_user_type, sys_user_sex } = proxy.useDict(
  "sys_user_type",
  "sys_user_sex"
);
const sys_user_enabled = [
  { label: "下架", value: "0" },
  { label: "上架", value: "1" },
];

const MiniUserList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 统计数据
const totalStaff = ref(0);
const activeStaff = ref(0);
const totalEarnings = ref(0);
const avgLevel = ref(0);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    openid: null,
    nickName: null,
    avatar: null,
    wechat: null,
    phone: null,
    balance: null,
    userType: null,
    createdAt: null,
    updatedAt: null,
    sex: null,
    age: null,
    province: null,
    tags: null,
    isOnline: null,
    basePrice: null,
    signature: null,
    level: null,
    totalAmount: null,
    voice: null,
    voiceTime: null,
  },
  rules: {
    openid: [{ required: true, message: "openid不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户管理列表 */
function getList() {
  loading.value = true;
  listMiniUser(queryParams.value).then((response) => {
    MiniUserList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 计算统计数据
    calculateStats();
  });
}

/** 计算统计数据 */
function calculateStats() {
  totalStaff.value = MiniUserList.value.length;
  activeStaff.value = MiniUserList.value.filter(
    (user) => user.enabled == 1 || user.userType == 3
  ).length;

  // 计算总收益（余额总和）
  totalEarnings.value = MiniUserList.value
    .reduce((sum, user) => {
      return sum + (parseFloat(user.balance) || 0);
    }, 0)
    .toFixed(2);

  // 计算平均等级
  const levels = MiniUserList.value
    .filter((user) => user.level)
    .map((user) => parseInt(user.level) || 0);
  avgLevel.value =
    levels.length > 0
      ? (levels.reduce((sum, level) => sum + level, 0) / levels.length).toFixed(
          1
        )
      : 0;
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    userId: null,
    openid: null,
    nickName: null,
    avatar: null,
    wechat: null,
    phone: null,
    balance: null,
    userType: null,
    createdAt: null,
    updatedAt: null,
    sex: null,
    age: null,
    province: null,
    tags: null,
    isOnline: null,
    basePrice: null,
    signature: null,
    level: null,
    totalAmount: null,
    voice: null,
    voiceTime: null,
  };
  proxy.resetForm("MiniUserRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用户管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _userId = row.userId || ids.value;
  getMiniUser(_userId).then((response) => {
    form.value = response.data;
    form.value.enabled = form.value.enabled == "1" ? "上架" : "下架";
    open.value = true;
    title.value = "修改用户";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["MiniUserRef"].validate((valid) => {
    if (valid) {
      if (form.value.userId != null) {
        updateMiniUser(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMiniUser(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _userIds = row.userId || ids.value;
  proxy.$modal
    .confirm('是否确认删除用户管理编号为"' + _userIds + '"的数据项？')
    .then(function () {
      return delMiniUser(_userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/MiniUser/export",
    {
      ...queryParams.value,
    },
    `MiniUser_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>

<style scoped>
.app-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
}

/* 统计卡片样式 */
.stats-card {
  margin-bottom: 16px;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  }
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 24px;
}

.stats-icon {
  margin-right: 20px;
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.3);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

/* 搜索卡片样式 */
.search-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

/* 操作按钮卡片样式 */
.action-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.action-btn span {
  margin-left: 4px;
}

.toolbar-wrapper {
  display: flex;
  justify-content: flex-end;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-subtitle {
  font-size: 14px;
  color: #6b7280;
}

/* 表格样式 */
.staff-table {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 表格操作按钮样式 */
.action-buttons-group {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.table-action-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 60px;
}

.table-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 分页组件样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.custom-pagination {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 12px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
