<template>
    <view class="user-detail">
        <!-- 头部 -->
        <view class="user-detail-header">
            <!-- 用户图片展示区 -->
            <swiper class="swiper-container" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
                circular>
                <swiper-item v-for="image in userImages" :key="image.id" @tap="previewImage(image.id)">
                    <image :src="image.url" mode="aspectFill" class="swiper-image" />
                </swiper-item>
            </swiper>
        </view>
        <!-- 内容 -->
        <view class="user-detail-content">
            <!-- 用户信息 -->
            <view class="user-info-card">
                <view class="avatar-container">
                    <image :src="userInfo.avatar" mode="widthFix" class="user-avatar" />
                    <view class="user-vedio" @tap="handleVoice(userInfo)" :class="[isPlaying ? 'playing' : '']">
                        {{ userInfo.voiceTime }}s
                    </view>
                    <view class=" user-level" :class="getLevelClass(userInfo)">
                        {{ getLevelText(userInfo) }}
                    </view>
                </view>
                <!-- 用户信息详情 -->
                <view class="user-info-content">
                    <view class="user-info-detail">
                        <view class="user-nickname">{{ userInfo.nickName }}</view>
                        <view class="user-point"></view>
                        <view class="user-online">{{ userInfo.isOnline ? '在线' : '离线' }}</view>
                        <view class="user-province">{{ userInfo.province }}</view>
                    </view>
                    <view class="user-sex-box">
                        <image
                            :src="userInfo.sex === 1 ? '../../static/iconfont/boy.png' : '../../static/iconfont/girl.png'"
                            mode="widthFix" class="user-sex-icon" />
                        <view class="user-sex" :class="[userInfo.sex === 1 ? 'boy' : 'girl']">
                            {{ userInfo.age }}
                        </view>
                    </view>
                    <view class="user-price-container">
                        <view class="user-price">{{ userInfo.basePrice }}虚拟币/起</view>
                    </view>
                    <view class="user-tags">
                        <view v-for="(tag, tagIndex) in userInfo.tags?.split(',')" :key="tagIndex" class="tag-item">
                            {{ tag }}
                        </view>
                    </view>
                    <view class="user-signature-label-box">
                        <view class="user-signature-label">签名:</view>
                        <view class="user-signature">{{ userInfo.signature }}</view>
                    </view>

                </view>
            </view>
            <!-- 打赏墙 -->
            <view class="gift-container">
                <view class="section-title">已收打赏</view>
                <scroll-view class="gift-wall" scroll-y>
                    <view class="reward-list">
                        <view class="reward-item" v-for="reward in rewardList" :key="reward.id">
                            <view class="reward-amount">{{ reward.totalPrice }}币</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <!-- 留言墙 -->
            <view class="message-container">
                <view class="section-title">留言墙</view>
                <scroll-view class="message-wall" scroll-y>
                    <view class="message-list">
                        <view class="message-item" v-for="message in messageList" :key="message.id"
                            @tap="handleMessageDetail(message)">
                            <view class="message-content">{{ message.content }}</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <!-- 服务类型与价格 -->
            <view class="service-type-price-box">
                <view class="section-title">服务类型与价格</view>
                <view class="service-table">
                    <!-- 表头 -->
                    <view class="table-header">
                        <view class="th">服务类型</view>
                        <view class="th">时长</view>
                        <view class="th">价格</view>
                    </view>
                    <!-- 表格内容 -->
                    <view class="table-row" v-for="service in serviceList" :key="service.id">
                        <view class="td service-type">{{ service.serviceType }}</view>
                        <view class="td time-list">
                            <view v-for="(duration, idx) in service.durations" :key="idx" class="time-item">
                                {{ duration.time }}
                            </view>
                        </view>
                        <view class="td price-list">
                            <view v-for="(duration, idx) in service.durations" :key="idx" class="price-item">
                                {{ duration.price }}虚拟币
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="user-detail-footer">
            <view class="footer-content" @tap="handleReward">
                打赏
            </view>
            <button class="footer-button" @tap="handleOrder">
                立即下单
            </button>
        </view>

        <!-- 留言详情弹窗 -->
        <view class="message-detail-mask" v-if="isShowMessageDetail" @tap="closeMessageDetail">
            <view class="message-detail-content" @tap.stop>
                <view class="message-detail-text">{{ currentMessage.content }}</view>
                <view class="message-detail-user">{{ currentMessage.userName }}</view>
                <view class="message-detail-close" @tap="closeMessageDetail">关闭</view>
            </view>
        </view>

        <!-- 打赏弹窗 -->
        <view class="modal-mask" v-if="showGiftModal" @tap="closeGiftModal"></view>
        <view class="modal-content gift-modal" v-if="showGiftModal" @tap.stop>
            <view class="modal-header">
                <text class="modal-title">给{{ userInfo.nickName }}打赏</text>
                <image src="../../static/iconfont/closed.png" class="close-icon" @tap="closeGiftModal" />
            </view>
            <view class="reward-form">
                <view class="form-item">
                    <text class="form-label">打赏金额（不少于{{ userInfo.basePrice }}虚拟币）</text>
                    <input type="number" v-model="rewardAmount" class="form-input" placeholder="请输入打赏金额" />
                </view>
            </view>
            <button class="confirm-btn" :class="{ 'btn-disabled': rewardAmount < userInfo.basePrice }"
                @tap="handleGiftConfirm">
                确认打赏
            </button>
        </view>

        <!-- 支付确认弹窗 -->
        <view class="modal-mask" v-if="showPayModal" @tap="closePayModal"></view>
        <view class="modal-content pay-modal" v-if="showPayModal" @tap.stop>
            <view class="modal-header">
                <text class="modal-title">确认支付</text>
                <image src="../../static/iconfont/closed.png" class="close-icon" @tap="closePayModal" />
            </view>

            <view class="pay-form">
                <view class="form-item">
                    <text class="form-label">微信号：（必填，非微信注明）</text>
                    <input type="text" v-model="payForm.wechat" placeholder="请输入微信号" class="form-input" />
                </view>

                <view class="form-item">
                    <text class="form-label">留言：( 选填 )</text>
                    <input type="text" v-model="payForm.message" placeholder="请输入留言内容" class="form-input" />
                </view>

                <view class="total-amount">
                    支付金额：<text class="amount">{{ totalPrice }}虚拟币</text>
                </view>
            </view>

            <button class="confirm-btn" :class="{ 'btn-disabled': !payForm.wechat }" @tap="handlePayConfirm">
                确认支付
            </button>
        </view>
        <!-- 下单弹窗 -->
        <view class="modal-mask" v-if="showOrderModal" @tap="closeOrderModal"></view>
        <view class="modal-content order-modal" v-if="showOrderModal" @tap.stop>
            <view class="modal-header">
                <text class="modal-title">给{{ userInfo.nickName }}下单</text>
                <image src="../../static/iconfont/closed.png" class="close-icon" @tap="closeOrderModal" />
            </view>

            <view class="order-form">
                <view class="form-item">
                    <text class="form-label">服务类型</text>
                    <view class="service-type-box">
                        <view class="service-type-item" v-for="service in serviceList" :key="service.id"
                            :class="[selectedService?.id === service.id ? 'active-service' : '']"
                            @tap="selectService(service)">
                            {{ service.serviceType }}
                        </view>
                    </view>
                </view>

                <view class="form-item" v-if="selectedService">
                    <text class="form-label">服务时长</text>
                    <view class="duration-box">
                        <view class="duration-item" v-for="duration in selectedService?.durations" :key="duration.id"
                            :class="[selectedDuration?.id === duration.id ? 'active-duration' : '']"
                            @tap="selectDuration(duration)">
                            {{ duration.time }}
                        </view>
                    </view>
                </view>
                <view class="form-item" v-if="selectedDuration">
                    <text class="form-label">数量</text>
                    <view class="quantity-control">
                        <view class="quantity-btn" @tap="handleOrderQuantity('minus')">-</view>
                        <input type="number" v-model="orderQuantity" class="quantity-input"
                            @input="handleOrderQuantityInput" />
                        <view class="quantity-btn" @tap="handleOrderQuantity('plus')">+</view>
                    </view>
                </view>

                <view class="total-price" v-if="selectedDuration">
                    总价：<text class="price-value">{{ orderTotalPrice }}虚拟币</text>
                </view>
            </view>

            <button class="confirm-btn" :class="{ 'btn-disabled': !canConfirmOrder }" @tap="handleOrderConfirm">
                确认下单
            </button>
        </view>
    </view>
</template>

<script setup>
import { onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getUserInfo } from '../../api/user'
import { ref, computed } from 'vue'
import { getLeaveMessageList } from '../../api/leaveMessage'
import { getUserServiceList } from '../../api/userService'
import { createGiftOrder, getGiftOrderList } from '../../api/giftOrder'
import { getImage } from '../../api/image'
const userId = ref(null)
const userInfo = ref({})

// 获取等级对应的样式类
const getLevelClass = (item) => {
    const levelMap = {
        '普通': 'level-normal',
        '金牌': 'level-gold',
        '镇店': 'level-shop',
        '男女神': 'level-god'
    }
    return levelMap[item?.level] || 'level-normal'
}

// 获取等级显示文本
const getLevelText = (item) => {
    if (item?.level === '男女神') {
        return item.sex === 1 ? '男神' : '女神'
    }
    return item?.level
}

// 打赏列表数据
const rewardList = ref([])

// 留言列表数据
const messageList = ref([])


// 留言详情相关
const isShowMessageDetail = ref(false)
const currentMessage = ref({})

// 显示留言详情
const handleMessageDetail = (message) => {
    currentMessage.value = message
    isShowMessageDetail.value = true
}

// 关闭留言详情
const closeMessageDetail = () => {
    isShowMessageDetail.value = false
}

// 处理服务列表数据，按serviceTypeId分组
const processServiceList = (services) => {
    // 按serviceTypeId分组
    const groupedServices = {}
    services.forEach(service => {
        if (!groupedServices[service.serviceTypeId]) {
            groupedServices[service.serviceTypeId] = {
                id: service.serviceTypeId,
                serviceType: service.serviceName,
                durations: []
            }
        }

        groupedServices[service.serviceTypeId].durations.push({
            id: service.id,
            time: service.serviceTime,
            price: service.servicePrice
        })
    })

    // 转换为数组
    return Object.values(groupedServices)
}

// 服务列表数据
const rawServiceList = ref([])
const serviceList = computed(() => {
    return processServiceList(rawServiceList.value)
})

// 添加音频相关变量
const isPlaying = ref(false)
const audioContext = ref(null)

// 添加音频播放处理函数
const handleVoice = (item) => {
    if (!item.voice) return;

    // 如果正在播放，则停止
    if (isPlaying.value) {
        if (audioContext.value) {
            audioContext.value.stop()
            audioContext.value = null
        }
        isPlaying.value = false
        return
    }

    // 创建新的音频实例
    const audio = uni.createInnerAudioContext()
    audioContext.value = audio
    audio.src = item.voice
    audio.obeyMuteSwitch = false

    // 配置音频事件
    audio.onPlay(() => {
        console.log('开始播放')
        isPlaying.value = true
    })

    audio.onEnded(() => {
        console.log('播放结束')
        audioContext.value = null
        isPlaying.value = false
    })

    audio.onError((err) => {
        console.error('播放错误:', err)
        audioContext.value = null
        isPlaying.value = false
        uni.showToast({
            title: '音频播放失败',
            icon: 'none'
        })
    })

    // 开始播放
    try {
        audio.play()
    } catch (err) {
        console.error('播放失败:', err)
        audioContext.value = null
        isPlaying.value = false
    }
}

// 组件卸载时清理音频资源
onUnmounted(() => {
    if (audioContext.value) {
        audioContext.value.stop()
        audioContext.value = null
    }
    isPlaying.value = false
})

onLoad((options) => {
    console.log(options.userId)
    userId.value = options.userId
    // 获取用户信息
    getUserInfo(userId.value).then(res => {
        userInfo.value = res
    })
    // 获取用户展示图片
    getImage({
        userId: userId.value
    }).then(res => {
        userImages.value = res || []
    })
    // 获取打赏列表
    getGiftOrderList({
        sellerId: userId.value,
        isReward: true
    }).then(res => {
        rewardList.value = res
    })
    // 获取留言列表
    getLeaveMessageList({
        userId: userId.value
    }).then(res => {
        messageList.value = res
    })
    // 获取服务列表
    getUserServiceList({
        userId: userId.value
    }).then(res => {
        rawServiceList.value = res
    })
})

// 打赏相关-------------------------------
const showGiftModal = ref(false)
const rewardAmount = ref('')
const totalPrice = ref(0)
const orderType = ref('')

// 打赏（自定义金额）
const handleReward = () => {
    openGiftModal()
}

// 打开打赏弹窗
const openGiftModal = () => {
    showGiftModal.value = true
}

// 关闭打赏弹窗
const closeGiftModal = () => {
    showGiftModal.value = false
    rewardAmount.value = ''
}

// 修改打赏确认函数
const handleGiftConfirm = () => {
    if (!rewardAmount.value) return
    const amount = Number(rewardAmount.value)
    if (amount < userInfo.value.basePrice) {
        uni.showToast({
            title: '请输入正确的打赏金额',
            icon: 'none'
        })
        return
    }

    getUserInfo(0).then(res => {
        if (amount > res.balance) {
            uni.showToast({
                title: '余额不足，请先充值',
                icon: 'none'
            })
        } else {
            showGiftModal.value = false
            totalPrice.value = amount
            payForm.value = {
                wechat: '',
                message: ''
            }
            orderType.value = 'reward'
            showPayModal.value = true
        }
    })
}

// 支付相关
const showPayModal = ref(false)
const payForm = ref({
    wechat: '',
    message: ''
})

// 关闭支付弹窗
const closePayModal = () => {
    showPayModal.value = false
    payForm.value = {
        wechat: '',
        message: ''
    }
}

// 修改确认下单函数
const handleOrderConfirm = () => {
    if (!canConfirmOrder.value) return

    // 查询当前用户余额
    getUserInfo(0).then(res => {
        if (orderTotalPrice.value > res.balance) {
            uni.showToast({
                title: '余额不足，请先充值',
                icon: 'none'
            })
        } else {
            // 关闭下单弹窗
            showOrderModal.value = false

            // 设置支付信息
            totalPrice.value = orderTotalPrice.value
            payForm.value = {
                wechat: '',
                message: ''
            }

            // 记录订单类型为服务订单
            orderType.value = 'service'

            // 显示支付弹窗
            showPayModal.value = true
        }
    })
}

// 下单相关
const showOrderModal = ref(false)
const selectedService = ref(null)
const selectedDuration = ref(null)
const orderQuantity = ref(1)

// 计算订单总价
const orderTotalPrice = computed(() => {
    if (!selectedDuration.value) return 0
    return selectedDuration.value.price * orderQuantity.value
})

// 是否可以确认下单
const canConfirmOrder = computed(() => {
    return selectedService.value && selectedDuration.value && orderQuantity.value > 0
})

// 打开下单弹窗
const handleOrder = () => {
    showOrderModal.value = true
}

// 关闭下单弹窗
const closeOrderModal = () => {
    showOrderModal.value = false
    selectedService.value = null
    selectedDuration.value = null
    orderQuantity.value = 1
}

// 选择服务类型
const selectService = (service) => {
    selectedService.value = service
    selectedDuration.value = null // 重置时长选择
}

// 选择服务时长
const selectDuration = (duration) => {
    selectedDuration.value = duration
    orderQuantity.value = 1
}

// 处理订单数量变化
const handleOrderQuantity = (type) => {
    if (type === 'minus' && orderQuantity.value > 1) {
        orderQuantity.value--
    } else if (type === 'plus') {
        orderQuantity.value++
    }
}

// 处理订单数量输入
const handleOrderQuantityInput = (e) => {
    const value = parseInt(e.detail.value)
    if (isNaN(value) || value < 1) {
        orderQuantity.value = 1
    }
}

// （最终）支付确认处理函数
const handlePayConfirm = () => {
    if (!payForm.value.wechat) {
        uni.showToast({
            title: '请输入微信号',
            icon: 'none'
        })
        return
    }
    const orderData = orderType.value === 'reward'
        ? {
            sellerId: userInfo.value.userId,
            isReward: true,
            totalPrice: totalPrice.value,
            wechat: payForm.value.wechat,
            message: payForm.value.message
        }
        : {
            sellerId: userInfo.value.userId,
            isReward: false,
            serviceId: selectedDuration.value.id,
            quantity: orderQuantity.value,
            totalPrice: totalPrice.value,
            wechat: payForm.value.wechat,
            message: payForm.value.message
        }

    console.log('支付信息:', orderData)

    createGiftOrder(orderData).then(res => {
        console.log(res)
        // 重新获取接收礼物列表
        getGiftOrderList({
            userId: userId.value
        }).then(res => {
            rewardList.value = res
        })
        // 重新获取留言列表
        getLeaveMessageList({
            userId: userId.value
        }).then(res => {
            messageList.value = res
        })
        uni.showToast({
            title: '下单成功',
            icon: 'success'
        })
        closePayModal()
    })
}

// 用户展示图片列表
const userImages = ref([])

// 预览图片
const previewImage = (id) => {
    uni.previewImage({
        urls: userImages.value.map(image => image.url),
        current: id
    })
}
</script>

<style lang="scss" scoped>
.user-detail {
    background-color: #f1f1f1;
    padding-bottom: 120rpx;

    .user-detail-header {
        .swiper-container {
            width: 100%;
            height: 400rpx;

            .swiper-image {
                width: 100%;
                height: 100%;
            }
        }
    }

    .user-detail-content {
        margin: 25rpx 15rpx;
        background-color: #ffffff;
        border-radius: 25rpx;

        .user-info-card {
            border-radius: 20rpx;
            padding: 30rpx 30rpx 0 30rpx;
            display: flex;

            .avatar-container {
                position: relative;
                margin-right: 40rpx;

                .user-avatar {
                    width: 220rpx;
                    height: 220rpx;
                    border-radius: 190rpx;
                }

                .user-vedio {
                    color: #fff;
                    font-weight: 600;
                    font-size: 26rpx;
                    text-align: center;
                    width: 120rpx;
                    padding: 6rpx 15rpx;
                    margin: 20rpx auto;
                    background-color: #5be4ff;
                    border-radius: 35rpx;

                    &.playing {
                        background-color: #ff6b6b;
                        position: relative;

                        &::after {
                            content: '';
                            position: absolute;
                            right: 10rpx;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 4rpx;
                            height: 16rpx;
                            background-color: #fff;
                            animation: soundWave 0.5s infinite alternate;
                        }

                        &::before {
                            content: '';
                            position: absolute;
                            right: 18rpx;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 4rpx;
                            height: 10rpx;
                            background-color: #fff;
                            animation: soundWave 0.5s infinite alternate-reverse;
                        }
                    }
                }

                .user-level {
                    top: 170rpx;
                    left: 148rpx;
                    position: absolute;
                    padding: 5rpx 15rpx;
                    border-radius: 40rpx;
                    font-size: 24rpx;
                    color: #fff;
                    font-weight: 600;

                    &.level-normal {
                        background-color: #ff95ca;
                    }

                    &.level-gold {
                        background-color: #ffd700;
                    }

                    &.level-shop {
                        background-color: #ff4500;
                    }

                    &.level-god {
                        background: linear-gradient(to right, #ff69b4, #ff1493);
                    }
                }
            }

            .user-info-content {
                flex: 1;
                display: flex;
                flex-direction: column;

                .user-info-detail {
                    display: flex;
                    align-items: center;

                    .user-nickname {
                        font-size: 32rpx;
                        font-weight: 600;
                    }

                    .user-point {
                        width: 10rpx;
                        height: 10rpx;
                        margin: 14rpx 10rpx 0 auto;
                        background-color: #55ff00;
                        border-radius: 10rpx;
                    }

                    .user-online {
                        margin-right: 20rpx;
                        font-size: 24rpx;
                        color: #454545;
                    }

                    .user-province {
                        font-size: 24rpx;
                        color: #454545;
                    }
                }

                .user-sex-box {
                    display: flex;
                    align-items: center;
                    margin-top: 12rpx;

                    .user-sex-icon {
                        width: 20rpx;
                        height: 20rpx;
                        margin: 0 10rpx 0 6rpx;
                    }

                    .user-sex {
                        font-size: 24rpx;
                        font-weight: 500;

                        &.girl {
                            color: #ff87c3;
                        }

                        &.boy {
                            color: #7bc0f9;
                        }
                    }
                }

                .user-tags {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 30rpx;
                    margin: 15rpx 0;

                    .tag-item {
                        padding: 2rpx 15rpx;
                        border-radius: 60rpx;
                        font-size: 20rpx;
                        color: #ff87c3;
                        border: 1rpx solid #ff87c3;
                        background-color: #fff;
                    }
                }

                .user-price-container {
                    margin: 10rpx 5rpx 5rpx 2rpx;

                    .user-price {
                        color: #ff4500;
                        font-size: 22rpx;
                    }

                }

                .user-signature-label-box {
                    margin: 5rpx 0 0 5rpx;

                    .user-signature-label {
                        font-size: 22rpx;
                        color: #000000;
                    }

                    .user-signature {
                        margin-top: 3rpx;
                        font-size: 22rpx;
                        color: #666;
                    }
                }

            }
        }
    }

    .gift-container {
        border-top: 1rpx solid #e5e5e5;
        margin: 0 20rpx;

        .section-title {
            font-size: 26rpx;
            font-weight: 600;
            color: #333;
            padding: 30rpx 10rpx 30rpx 10rpx;
        }

        .gift-wall {
            max-height: calc(25vw * 3 + 40rpx);

            .reward-list {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 20rpx;
                padding: 20rpx;

                .reward-item {
                    background: #fff;
                    border-radius: 12rpx;
                    padding: 20rpx;
                    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
                    text-align: center;

                    .reward-amount {
                        font-size: 24rpx;
                        color: #ff6b6b;
                    }
                }
            }
        }
    }

    .message-container {
        border-top: 1rpx solid #e5e5e5;
        margin: 0 20rpx;

        .section-title {
            font-size: 26rpx;
            font-weight: 600;
            color: #333;
            padding: 30rpx 10rpx 30rpx 10rpx;
        }

        .message-wall {
            max-height: calc(25vw * 3 + 40rpx);

            .message-list {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 20rpx;
                padding: 20rpx;

                .message-item {
                    background: #fff;
                    border-radius: 12rpx;
                    padding: 20rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    min-height: 80rpx;

                    .message-content {
                        font-size: 24rpx;
                        color: #666;
                        text-align: center;
                        word-break: break-all;
                    }
                }
            }
        }
    }

    .service-type-price-box {
        border-top: 1rpx solid #e5e5e5;
        margin: 0 20rpx;

        .section-title {
            font-size: 26rpx;
            font-weight: 600;
            color: #333;
            padding: 30rpx 10rpx 30rpx 10rpx;
        }

        .service-table {
            background: #fff;
            border-radius: 12rpx;
            overflow: hidden;

            .table-header {
                display: flex;
                background-color: #f8f8f8;
                padding: 20rpx 0;

                .th {
                    flex: 1;
                    text-align: center;
                    font-size: 24rpx;
                    color: #333;
                    font-weight: 600;
                }
            }

            .table-row {
                display: flex;
                border-bottom: 1rpx solid #f5f5f5;

                &:last-child {
                    border-bottom: none;
                }

                .td {
                    flex: 1;
                    text-align: center;
                    padding: 20rpx 0;
                    font-size: 24rpx;
                    color: #666;

                    &.time-list,
                    &.price-list {
                        display: flex;
                        flex-direction: column;

                        .time-item,
                        .price-item {
                            padding: 6rpx 0;
                        }
                    }
                }
            }
        }
    }
}

// 留言详情弹窗样式
.message-detail-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    .message-detail-content {
        width: 80%;
        background: #fff;
        border-radius: 20rpx;
        padding: 40rpx;
        position: relative;

        .message-detail-text {
            font-size: 28rpx;
            color: #333;
            line-height: 1.6;
            margin-bottom: 20rpx;
        }

        .message-detail-user {
            font-size: 24rpx;
            color: #999;
            text-align: right;
        }

        .message-detail-close {
            position: absolute;
            bottom: -80rpx;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            font-size: 28rpx;
            padding: 10rpx 40rpx;
            border-radius: 40rpx;
            background: rgba(255, 255, 255, 0.3);
        }
    }
}

.user-detail-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 20rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    border-top: 1rpx solid #e5e5e5;

    .footer-content {
        font-size: 28rpx;
        color: #ff0000;
        font-weight: 600;
        padding: 0 50rpx;
    }

    .footer-button {
        background-color: #00d9ff;
        color: #fff;
        font-size: 28rpx;
        font-weight: 600;
        padding: 10rpx 40rpx;
        border-radius: 40rpx;
        width: 400rpx;
    }

}

// 添加音频波纹动画
@keyframes soundWave {
    from {
        height: 10rpx;
    }

    to {
        height: 20rpx;
    }
}

// 弹窗相关样式
.modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
}

.gift-modal {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 500rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    z-index: 1000;

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20rpx;

        .modal-title {
            font-size: 26rpx;
            font-weight: 600;
        }

        .close-icon {
            width: 40rpx;
            height: 40rpx;
            padding: 10rpx;
        }
    }

    .reward-form {
        padding: 20rpx 0;

        .form-item {
            margin-bottom: 30rpx;

            .form-label {
                display: block;
                font-size: 28rpx;
                color: #333;
                margin-bottom: 16rpx;
            }

            .form-input {
                width: 90%;
                height: 80rpx;
                background: #f5f5f5;
                border-radius: 12rpx;
                padding: 0 20rpx;
                font-size: 26rpx;
            }
        }
    }

    .confirm-btn {
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        background: #00d9ff;
        color: #fff;
        border-radius: 40rpx;
        font-size: 30rpx;
        margin-top: 20rpx;

        &.btn-disabled {
            background: #ccc;
        }
    }
}

.pay-modal {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 500rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    z-index: 1000;

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20rpx;

        .modal-title {
            font-size: 32rpx;
            font-weight: 600;
        }

        .close-icon {
            width: 40rpx;
            height: 40rpx;
            padding: 10rpx;
        }
    }

    .pay-form {
        padding: 20rpx 0;

        .form-item {
            margin-bottom: 30rpx;

            .form-label {
                display: block;
                font-size: 26rpx;
                color: #333;
                margin-bottom: 16rpx;
            }

            .form-input {
                width: 90%;
                height: 80rpx;
                background: #f5f5f5;
                border-radius: 12rpx;
                padding: 0 20rpx;
                font-size: 26rpx;
            }

            .form-textarea {
                width: 100%;
                height: 160rpx;
                background: #f5f5f5;
                border-radius: 12rpx;
                padding: 20rpx;
                font-size: 26rpx;
            }
        }

        .total-amount {
            text-align: right;
            font-size: 28rpx;
            color: #333;
            margin-top: 30rpx;

            .amount {
                color: #00d9ff;
                font-weight: 600;
            }
        }
    }

    .confirm-btn {
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        background: #00d9ff;
        color: #fff;
        border-radius: 40rpx;
        font-size: 30rpx;
        margin-top: 20rpx;

        &.btn-disabled {
            background: #ccc;
        }
    }
}

.order-modal {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 600rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    z-index: 1000;

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20rpx;

        .modal-title {
            font-size: 32rpx;
            font-weight: 600;
        }

        .close-icon {
            width: 40rpx;
            height: 40rpx;
            padding: 10rpx;
        }
    }

    .order-form {
        padding: 20rpx 0;

        .form-item {
            margin-bottom: 30rpx;

            .form-label {
                display: block;
                font-size: 28rpx;
                color: #333;
                margin-bottom: 20rpx;
            }

            .service-type-box {
                display: flex;
                flex-wrap: wrap;
                gap: 20rpx;

                .service-type-item {
                    padding: 15rpx 30rpx;
                    border: 1rpx solid #ddd;
                    border-radius: 30rpx;
                    font-size: 26rpx;
                    color: #666;

                    &.active-service {
                        background-color: #00d9ff;
                        color: #fff;
                        border-color: #00d9ff;
                    }
                }
            }

            .duration-box {
                display: flex;
                flex-wrap: wrap;
                gap: 20rpx;

                .duration-item {
                    padding: 15rpx 30rpx;
                    border: 1rpx solid #ddd;
                    border-radius: 30rpx;
                    font-size: 26rpx;
                    color: #666;

                    &.active-duration {
                        background-color: #00d9ff;
                        color: #fff;
                        border-color: #00d9ff;
                    }
                }
            }

            .quantity-control {
                display: flex;
                align-items: center;
                width: fit-content;

                .quantity-btn {
                    width: 60rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    text-align: center;
                    background: #f5f5f5;
                    border-radius: 30rpx;
                }

                .quantity-input {
                    width: 100rpx;
                    height: 60rpx;
                    text-align: center;
                    margin: 0 20rpx;
                }
            }
        }

        .total-price {
            text-align: right;
            font-size: 28rpx;
            color: #333;
            margin-top: 30rpx;

            .price-value {
                color: #00d9ff;
                font-weight: 600;
            }
        }
    }

    .confirm-btn {
        width: 100%;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        background: #00d9ff;
        color: #fff;
        border-radius: 40rpx;
        font-size: 30rpx;
        margin-top: 20rpx;

        &.btn-disabled {
            background: #ccc;
        }
    }
}
</style>
