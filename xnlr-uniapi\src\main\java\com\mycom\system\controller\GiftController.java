package com.mycom.system.controller;

import com.mycom.system.domain.GiftEntity;
import com.mycom.system.service.impl.GiftService;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.mycom.system.common.vo.Result;

import javax.annotation.Resource;
import java.util.List;

/**
 * (Gift)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-25 10:29:21
 */
@RestController
@RequestMapping("gift")
public class GiftController {
    /**
     * 服务对象
     */
    @Resource
    private GiftService giftService;

    /**
     * 通过条件筛选，分页查询
     *
     * @param giftEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list/page")
    public Result<PageInfo<GiftEntity>> getListPage(@RequestBody GiftEntity giftEntity) {
        return Result.success(giftService.getListPage(giftEntity));
    }

    /**
     * 通过条件筛选,全部
     *
     * @param giftEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list")
    public Result<List<GiftEntity>> getList(@RequestBody GiftEntity giftEntity) {
        return Result.success(giftService.getList(giftEntity));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键 实体
     * @return 单条数据
     */
    @GetMapping("{id}")
    public Result<GiftEntity> queryById(@PathVariable("id") Long id) {
        return Result.success(giftService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param giftEntity 实体
     * @return 新增结果
     */
    @PostMapping
    public Result<GiftEntity> add(GiftEntity giftEntity) {
        return Result.success(giftService.insert(giftEntity));
    }

    /**
     * 编辑数据
     *
     * @param giftEntity 实体
     * @return 编辑结果
     */
    @PutMapping
    public Result<GiftEntity> edit(GiftEntity giftEntity) {
        return Result.success(giftService.update(giftEntity));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public Result<Boolean> deleteById(Long id) {
        return Result.success(giftService.deleteById(id));
    }

}

