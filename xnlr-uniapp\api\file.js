import request from '../utils/request.js'

// 上传头像
export const uploadAvatar = (file) => request.post('/syswx/file/upload/avatar', file,'multipart/form-data')

// 选择头像
export const chooseAvatar = () => {
	return new Promise((resolve, reject) => {
		uni.chooseImage({
			count: 1,
			success: async (res) => {
				try {
					const filePath = res.tempFilePaths[0]
					const token = uni.getStorageSync('token');
					uni.uploadFile({
						url: 'http://localhost:8080/user/avatar/upload',
						filePath: filePath,
						name: 'file',
						header: {
							'Authorization': `Bearer ${token}`,
						},
						success: (uploadRes) => {
							const data = JSON.parse(uploadRes.data)
							if (data.code === 200) {
								resolve(data.data)
							} else {
								reject(new Error(data.msg || '上传失败'))
							}
						},
						fail: (error) => {
							reject(error)
						}
					})
				} catch (error) {
					console.error('头像上传失败:', error)
					uni.showToast({
						title: '头像上传失败',
						icon: 'none'
					})
					reject(error)
				}
			},
			fail: (error) => {
				reject(error)
			}
		})
	})
}