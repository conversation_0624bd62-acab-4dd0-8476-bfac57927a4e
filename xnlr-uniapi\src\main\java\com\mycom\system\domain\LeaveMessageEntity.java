package com.mycom.system.domain;

import java.io.Serializable;

import lombok.Data;


/**
 * (LeaveMessage)实体类
 *
 * <AUTHOR>
 * @since 2025-02-23 10:17:12
 */


@Data

public class LeaveMessageEntity implements Serializable {
    private static final long serialVersionUID = -31081103331212662L;
    /**
     * 留言id
     */
    private Long id;

    /**
     * 关联的用户id
     */
    private Object userId;

    /**
     * 留言内容
     */
    private String content;


    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

}

