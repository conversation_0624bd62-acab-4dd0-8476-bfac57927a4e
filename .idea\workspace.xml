<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3bd1379f-47e8-40d2-a76f-869069e9b400" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/DashboardController.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/DashboardController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/MiniUserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/MiniUserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/UserApplyController.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/UserApplyController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/mapper/DashboardMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/mapper/DashboardMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/mapper/MiniUserMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/mapper/MiniUserMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/IDashboardService.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/IDashboardService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/IMiniUserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/IMiniUserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/IUserApplyService.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/IUserApplyService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/DashboardServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/DashboardServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/MiniUserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/MiniUserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/UserApplyServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/UserApplyServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/resources/mapper/system/DashboardMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/resources/mapper/system/DashboardMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/resources/mapper/system/MiniUserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-adminapi/ruoyi-system/src/main/resources/mapper/system/MiniUserMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-ui/src/api/system/UserApply.js" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-ui/src/api/system/UserApply.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-ui/src/api/system/dashboard.js" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-ui/src/api/system/dashboard.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-ui/src/components/Pagination/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-ui/src/components/Pagination/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-ui/src/plugins/modal.js" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-ui/src/plugins/modal.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-ui/src/views/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-ui/src/views/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-ui/src/views/system/UserApply/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-ui/src/views/system/UserApply/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-uniapi/src/main/resources/mapper/UserMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-uniapi/src/main/resources/mapper/UserMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/xnlr-uniapp/pages/background-manage/background-manage.vue" beforeDir="false" afterPath="$PROJECT_DIR$/xnlr-uniapp/pages/background-manage/background-manage.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/xnlr-adminapi" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\soft\apache-maven-3.9.8-bin\apache-maven-3.9.8" />
        <option name="localRepository" value="E:\soft\apache-maven-3.9.8-bin\apache-maven-3.9.8\mvn_resp" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\soft\apache-maven-3.9.8-bin\apache-maven-3.9.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2yTai1xOsTcb2J5v0dar5b0ohJb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.system [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.system [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ApplicationRun.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/wechat/cpcat&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.projectsettings.compiler.annotationProcessors&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.RuoYiApplication">
    <configuration name="ApplicationRun" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.mycom.system.ApplicationRun" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3bd1379f-47e8-40d2-a76f-869069e9b400" name="更改" comment="" />
      <created>1749861140212</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749861140212</updated>
      <workItem from="1749861141309" duration="572000" />
      <workItem from="1749861792080" duration="2161000" />
      <workItem from="1749881359492" duration="2424000" />
      <workItem from="1749884356361" duration="387000" />
      <workItem from="1749884771644" duration="920000" />
      <workItem from="1749888003315" duration="7322000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>