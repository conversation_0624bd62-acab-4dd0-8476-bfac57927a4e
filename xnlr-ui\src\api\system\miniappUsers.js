import request from '@/utils/request'

// 查询小程序用户列表
export function listMiniappUsers(query) {
  return request({
    url: '/system/miniapp-users/list',
    method: 'get',
    params: query
  })
}

// 查询小程序用户详细
export function getMiniappUser(userId) {
  return request({
    url: '/system/miniapp-users/' + userId,
    method: 'get'
  })
}

// 新增小程序用户
export function addMiniappUser(data) {
  return request({
    url: '/system/miniapp-users',
    method: 'post',
    data: data
  })
}

// 修改小程序用户
export function updateMiniappUser(data) {
  return request({
    url: '/system/miniapp-users',
    method: 'put',
    data: data
  })
}

// 删除小程序用户
export function delMiniappUser(userId) {
  return request({
    url: '/system/miniapp-users/' + userId,
    method: 'delete'
  })
}

// 导出小程序用户
export function exportMiniappUsers(query) {
  return request({
    url: '/system/miniapp-users/export',
    method: 'post',
    params: query
  })
}

// 获取小程序用户统计数据
export function getMiniappUsersStats() {
  return request({
    url: '/system/miniapp-users/stats',
    method: 'get'
  })
}
