<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.mycom.system.domain.UserEntity">
        <!--@Table user-->
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="openid" column="openid" jdbcType="VARCHAR"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="wechat" column="wechat" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="balance" column="balance" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="sex" column="sex" jdbcType="INTEGER"/>
        <result property="age" column="age" jdbcType="INTEGER"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"/>
        <result property="isOnline" column="is_online" jdbcType="VARCHAR"/>
        <result property="basePrice" column="base_price" jdbcType="INTEGER"/>
        <result property="signature" column="signature" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_id,
        openid,
        nick_name,
        avatar,
        wechat,
        phone,
        balance,
        user_type,
        created_at,
        updated_at,
        sex,
        age,
        province,
        tags,
        is_online,
        base_price,
        signature,
        level,
        voice,
        voice_time,
        total_amount
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user
        where user_id = #{userId}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select user_id,
               openid,
               nick_name,
               avatar,
               wechat,
               phone,
               balance,
               user_type,
               created_at,
               updated_at
        from user
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from user
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="openid != null and openid != ''">
                and openid = #{openid}
            </if>
            <if test="nickName != null and nickName != ''">
                and nick_name = #{nickName}
            </if>
            <if test="avatar != null and avatar != ''">
                and avatar = #{avatar}
            </if>
            <if test="wechat != null and wechat != ''">
                and wechat = #{wechat}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="balance != null">
                and balance = #{balance}
            </if>
            <if test="userType != null">
                and user_type = #{userType}
            </if>
            <if test="createdAt != null">
                and created_at = #{createdAt}
            </if>
            <if test="updatedAt != null">
                and updated_at = #{updatedAt}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="userId" useGeneratedKeys="true">
        insert into user(openid, nick_name, avatar, wechat, phone, balance, user_type, created_at, updated_at)
        values (#{openid}, #{nickName}, #{avatar}, #{wechat}, #{phone}, #{balance}, #{userType}, #{createdAt},
        #{updatedAt})
    </insert>


    <insert id="insertSelective" keyProperty="userId" useGeneratedKeys="true">
        insert into user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">
                openid,
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name,
            </if>
            <if test="avatar != null and avatar != ''">
                avatar,
            </if>
            <if test="wechat != null and wechat != ''">
                wechat,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="balance != null">
                balance,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">
                #{openid},
            </if>
            <if test="nickName != null and nickName != ''">
                #{nickName},
            </if>
            <if test="avatar != null and avatar != ''">
                #{avatar},
            </if>
            <if test="wechat != null and wechat != ''">
                #{wechat},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="balance != null">
                #{balance},
            </if>
            <if test="userType != null">
                #{userType},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="updatedAt != null">
                #{updatedAt},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into user(openid, nick_name, avatar, wechat, phone, balance, user_type, created_at, updated_at)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.openid}, #{entity.nickName}, #{entity.avatar}, #{entity.wechat}, #{entity.phone},
            #{entity.balance}, #{entity.userType}, #{entity.createdAt}, #{entity.updatedAt})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into user(openid, nick_name, avatar, wechat, phone, balance, user_type, created_at, updated_at)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.openid}, #{entity.nickName}, #{entity.avatar}, #{entity.wechat}, #{entity.phone},
            #{entity.balance}, #{entity.userType}, #{entity.createdAt}, #{entity.updatedAt})
        </foreach>
        on duplicate key update
        openid = values(openid),
        nick_name = values(nick_name),
        avatar = values(avatar),
        wechat = values(wechat),
        phone = values(phone),
        balance = values(balance),
        user_type = values(user_type),
        created_at = values(created_at),
        updated_at = values(updated_at)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update user
        <set>
            <if test="openid != null and openid != ''">
                openid = #{openid},
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName},
            </if>
            <if test="avatar != null and avatar != ''">
                avatar = #{avatar},
            </if>
            <if test="wechat != null and wechat != ''">
                wechat = #{wechat},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="balance != null">
                balance = #{balance},
            </if>
            <if test="userType != null">
                user_type = #{userType},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
        </set>
        where user_id = #{userId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from user where user_id = #{userId}
    </delete>

    <!--条件查询-->
    <select id="getList" resultMap="BaseResultMap">
        select user_id,nick_name, avatar,
        user_type,age,level,sex,province,tags,is_online,base_price,signature,voice,voice_time,total_amount,balance
        from user
        <where>
            <if test="searchKey != null and searchKey != ''">
                and (nick_name like concat('%', #{searchKey}, '%')
                or province like concat('%', #{searchKey}, '%'))
            </if>
            <if test="isOnline != null">
                and is_online = #{isOnline}
            </if>
            <if test="sex != null">
                and sex = #{sex}
            </if>
            <if test="level != null and level != ''">
                and level = #{level}
            </if>
            <!-- 查询客户用户(0)和申请成为店员的用户(3) -->
            and user_type in (0, 3)
        </where>
    </select>

    <select id="getUserByOpenId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user
        where openid = #{openid}
    </select>

    <select id="selectUserByUserName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user
        where nick_name = #{username}
    </select>

    <!-- ========== 数据统计相关查询 ========== -->

    <!-- 统计总用户数 -->
    <select id="countTotalUsers" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user
    </select>

    <!-- 统计在线用户数 -->
    <select id="countOnlineUsers" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user WHERE is_online = 1
    </select>

    <!-- 统计服务提供者数量 -->
    <select id="countServiceProviders" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user WHERE user_type = 1
    </select>

    <!-- 统计用户类型分布 -->
    <select id="getUserTypeStats" resultType="com.mycom.system.domain.vo.StatItem">
        SELECT
            CASE
                WHEN user_type = 0 THEN '普通用户'
                WHEN user_type = 1 THEN '服务提供者'
                WHEN user_type = 2 THEN '管理员'
                ELSE '未知'
            END as name,
            CAST(user_type AS CHAR) as value,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user), 2) as percentage
        FROM user
        WHERE user_type IS NOT NULL
        GROUP BY user_type
        ORDER BY count DESC
    </select>

    <!-- 统计性别分布 -->
    <select id="getGenderStats" resultType="com.mycom.system.domain.vo.StatItem">
        SELECT
            CASE
                WHEN sex = 0 THEN '女'
                WHEN sex = 1 THEN '男'
                ELSE '未知'
            END as name,
            CAST(sex AS CHAR) as value,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user WHERE sex IS NOT NULL), 2) as percentage
        FROM user
        WHERE sex IS NOT NULL
        GROUP BY sex
        ORDER BY count DESC
    </select>

    <!-- 统计在线状态分布 -->
    <select id="getOnlineStats" resultType="com.mycom.system.domain.vo.StatItem">
        SELECT
            CASE
                WHEN is_online = 1 THEN '在线'
                WHEN is_online = 0 THEN '离线'
                ELSE '未知'
            END as name,
            CAST(is_online AS CHAR) as value,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user WHERE is_online IS NOT NULL), 2) as percentage
        FROM user
        WHERE is_online IS NOT NULL
        GROUP BY is_online
        ORDER BY count DESC
    </select>

    <!-- 统计地域分布(TOP 10) -->
    <select id="getProvinceStats" resultType="com.mycom.system.domain.vo.StatItem">
        SELECT
            province as name,
            province as value,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user WHERE province IS NOT NULL AND province != ''), 2) as percentage
        FROM user
        WHERE province IS NOT NULL AND province != ''
        GROUP BY province
        ORDER BY count DESC
        LIMIT 10
    </select>

</mapper>

