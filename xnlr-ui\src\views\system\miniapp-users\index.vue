<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#409EFF"><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ totalUsers }}</div>
              <div class="stats-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#67C23A"><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ activeUsers }}</div>
              <div class="stats-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#E6A23C"><Wallet /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ totalBalance }}</div>
              <div class="stats-label">总余额</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#F56C6C"><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">¥{{ totalRecharge }}</div>
              <div class="stats-label">总充值</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="search-card mb-4">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        label-width="80px"
      >
        <el-form-item label="用户昵称" prop="nickName">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入用户昵称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入手机号"
            clearable
            style="width: 180px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select
            v-model="queryParams.sex"
            placeholder="请选择性别"
            clearable
            style="width: 150px"
          >
            <el-option label="男" value="0" />
            <el-option label="女" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input
            v-model="queryParams.province"
            placeholder="请输入省份"
            clearable
            style="width: 150px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:miniapp-users:export']"
            style="margin-left: 16px"
          >
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">小程序用户列表</span>
          <span class="card-subtitle">共 {{ total }} 名用户</span>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        class="user-table"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="用户信息" min-width="200" align="center">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :size="50" :src="scope.row.avatar" class="user-avatar">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
              <div class="user-details">
                <div class="user-name">{{ scope.row.nickName }}</div>
                <div class="user-id">ID: {{ scope.row.userId }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="详细信息" align="left" min-width="450">
          <template #default="scope">
            <div class="user-details-layout">
              <div class="left-section">
                <div class="contact-info">
                  <div v-if="scope.row.phone" class="info-item">
                    <el-icon><Phone /></el-icon>
                    <span>{{ scope.row.phone }}</span>
                  </div>
                  <div v-if="scope.row.wechat" class="info-item">
                    <el-icon><ChatDotRound /></el-icon>
                    <span>{{ scope.row.wechat }}</span>
                  </div>
                </div>
                <div class="basic-info">
                  <el-tag
                    :type="scope.row.sex === '0' ? 'primary' : 'danger'"
                    size="small"
                  >
                    {{ scope.row.sex === "0" ? "男" : "女" }}
                  </el-tag>
                  <span class="ml-2">{{ scope.row.age }}岁</span>
                  <div class="mt-1">{{ scope.row.province }}</div>
                </div>
              </div>
              <div class="right-section">
                <div class="account-info">
                  <div class="balance">余额: ¥{{ scope.row.balance || 0 }}</div>
                  <div class="recharge">
                    充值: ¥{{ scope.row.totalAmount || 0 }}
                  </div>
                  <div class="register-time">
                    注册: {{ formatDate(scope.row.createdAt) }}
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-tag
              :type="scope.row.enabled ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.enabled ? "正常" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          min-width="220"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <div class="action-buttons-group">
              <el-button
                size="small"
                type="primary"
                icon="View"
                @click="handleView(scope.row)"
                v-hasPermi="['system:miniapp-users:query']"
                class="table-action-btn"
              >
                查看
              </el-button>
              <el-button
                size="small"
                type="warning"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:miniapp-users:edit']"
                class="table-action-btn"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                :type="scope.row.enabled ? 'danger' : 'success'"
                icon="Switch"
                @click="handleStatusChange(scope.row)"
                v-hasPermi="['system:miniapp-users:edit']"
                class="table-action-btn"
                plain
              >
                {{ scope.row.enabled ? "禁用" : "启用" }}
              </el-button>
              <el-button
                size="small"
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:miniapp-users:remove']"
                class="table-action-btn"
                plain
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          class="custom-pagination"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup name="MiniappUsers">
import {
  listMiniappUsers,
  getMiniappUser,
  updateMiniappUser,
  delMiniappUser,
  getMiniappUsersStats,
} from "@/api/system/miniappUsers";
import {
  UserFilled,
  CircleCheck,
  Wallet,
  TrendCharts,
  Phone,
  ChatDotRound,
} from "@element-plus/icons-vue";

const { proxy } = getCurrentInstance();

const userList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 统计数据
const totalUsers = ref(0);
const activeUsers = ref(0);
const totalBalance = ref(0);
const totalRecharge = ref(0);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    nickName: null,
    phone: null,
    sex: null,
    province: null,
    userType: 0, // 普通用户类型
  },
});

const { queryParams } = toRefs(data);

/** 查询用户列表 */
function getList() {
  loading.value = true;
  listMiniappUsers(queryParams.value).then((response) => {
    userList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取统计数据 */
function getStats() {
  getMiniappUsersStats().then((response) => {
    totalUsers.value = response.data.totalUsers;
    activeUsers.value = response.data.activeUsers;
    totalBalance.value = response.data.totalBalance;
    totalRecharge.value = response.data.totalRecharge;
  });
}

/** 格式化日期 */
function formatDate(date) {
  if (!date) return "-";
  return new Date(date).toLocaleDateString();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看按钮操作 */
function handleView(row) {
  // 实现查看详情逻辑
  proxy.$modal.msgInfo("查看用户详情功能待实现");
}

/** 修改按钮操作 */
function handleUpdate(row) {
  // 实现编辑逻辑
  proxy.$modal.msgInfo("编辑用户功能待实现");
}

/** 状态切换 */
function handleStatusChange(row) {
  const text = row.enabled ? "禁用" : "启用";
  proxy.$modal
    .confirm('确认要"' + text + '""' + row.nickName + '"用户吗？')
    .then(function () {
      const updateData = {
        userId: row.userId,
        enabled: row.enabled ? 0 : 1,
      };
      return updateMiniappUser(updateData);
    })
    .then(() => {
      getList();
      getStats();
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(() => {});
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.userId || ids.value;
  proxy.$modal
    .confirm('是否确认删除用户编号为"' + userIds + '"的数据项？')
    .then(function () {
      return delMiniappUser(userIds);
    })
    .then(() => {
      getList();
      getStats();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/miniapp-users/export",
    {
      ...queryParams.value,
    },
    `miniapp_users_${new Date().getTime()}.xlsx`
  );
}

// 初始化数据
getList();
getStats();
</script>

<style scoped>
.stats-card {
  margin-bottom: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
}

.stats-icon {
  margin-right: 12px;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 13px;
  color: #909399;
  margin-top: 2px;
}

.search-card {
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.card-subtitle {
  font-size: 14px;
  color: #909399;
}

.user-table {
  border-radius: 6px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  border: 1px solid #f0f0f0;
}

.user-details {
  text-align: left;
}

.user-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 2px;
  font-size: 14px;
}

.user-id {
  font-size: 11px;
  color: #909399;
}

.user-details-layout {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.left-section {
  flex: 1;
  min-width: 0;
}

.right-section {
  flex: 1;
  min-width: 0;
}

.contact-info .info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
  font-size: 13px;
}

.basic-info {
  margin-top: 8px;
}

.account-info div {
  margin-bottom: 3px;
  font-size: 13px;
}

.balance {
  color: #67c23a;
  font-weight: bold;
}

.recharge {
  color: #e6a23c;
}

.register-time {
  color: #909399;
  font-size: 11px;
}

.mb-4 {
  margin-bottom: 12px;
}

.mt-1 {
  margin-top: 2px;
}

.ml-2 {
  margin-left: 6px;
}

/* 表格操作按钮样式 */
.action-buttons-group {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.table-action-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 60px;
}

.table-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 分页组件样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.custom-pagination {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 12px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}
</style>
