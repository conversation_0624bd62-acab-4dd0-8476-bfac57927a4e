package com.mycom.system.common.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mycom.system.common.exception.ErrorType;
import com.mycom.system.common.exception.SystemErrorType;
import lombok.Data;

@Data
public class Result<T> {

    public static final Integer SUCCESSFUL_CODE = 200;
    public static final String SUCCESSFUL_MSG = "处理成功";

    private Integer code;
    private String msg;
    private T data;

    public Result() {
    }

    /**
     * @param msg
     */
    public Result(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 内部使用，用于构造成功的结果
     *
     * @param code
     * @param msg
     * @param data
     */
    private Result(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 快速创建成功结果并返回结果数据
     *
     * @param data
     * @return Result
     */
    public static Result success(Object data) {
        return new Result<>(SUCCESSFUL_CODE, SUCCESSFUL_MSG, data);
    }

    /**
     * 快速创建成功结果
     *
     * @return Result
     */
    public static Result success() {
        return success(null);
    }

    /**
     * 快速创建成功结果并返回结果数据
     *
     * @param rows
     * @return Result
     */
    public static Result success(int rows) {
        return rows > 0 ? success() : new Result(SystemErrorType.SYSTEM_ERROR.getCode(), "操作失败");
    }

    /**
     * 系统异常类没有返回数据
     *
     * @return Result
     */
    public static Result fail() {
        return new Result(SystemErrorType.SYSTEM_ERROR.getCode(), SystemErrorType.SYSTEM_ERROR.getMsg());
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param errorType
     * @param data
     * @return Result
     */
    public static Result fail(ErrorType errorType, Object data) {
        return new Result<>(errorType.getCode(), errorType.getMsg(), data);
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param errorType
     * @return Result
     */
    public static Result fail(ErrorType errorType) {
        return Result.fail(errorType, errorType.getMsg());
    }

    /**
     * 系统异常类并返回结果数据
     *
     * @param msg
     * @return Result
     */
    public static Result fail(String msg) {
        return new Result<>(SystemErrorType.SYSTEM_ERROR.getCode(), msg);
    }

    /**
     * 系统异常类
     *
     * @param errorType
     * @return Result
     */
    public static Result fail(ErrorType errorType, String message) {
        return new Result<>(errorType.getCode(), message, null);
    }

    /**
     * 系统异常类
     */
    public static Result fail(Integer code, String msg) {
        return new Result(code, msg, null);
    }

    /**
     * 成功code=000000
     *
     * @return true/false
     */
    @JsonIgnore
    public boolean isSuccess() {
        return SUCCESSFUL_CODE.equals(this.code);
    }

    /**
     * 失败
     *
     * @return true/false
     */
    @JsonIgnore
    public boolean isFail() {
        return !isSuccess();
    }

}
