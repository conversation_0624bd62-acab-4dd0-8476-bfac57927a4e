package com.mycom.system.mapper;

import com.mycom.system.domain.GiftReceiveEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (gift)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-23 09:59:02
 */
public interface GiftReceiveMapper {
    /**
     * 条件查询
     *
     * @param giftReceiveEntity 查询条件
     * @return 对象列表
     */
    List<GiftReceiveEntity> getList(GiftReceiveEntity giftReceiveEntity);

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    GiftReceiveEntity selectById(Long id);

    void update(GiftReceiveEntity giftReceiveEntity);

    GiftReceiveEntity selectByUserIdAndGiftId(@Param("userId") Long userId, @Param("giftId") Long giftId);

    void insert(GiftReceiveEntity giftReceiveEntity);

//
//    /**
//     * 统计总行数
//     *
//     * @param giftReceiveEntity 查询条件
//     * @return 总行数
//     */
//    long count(GiftReceiveEntity giftReceiveEntity);
//
//    /**
//     * 新增数据
//     *
//     * @param giftReceiveEntity 实例对象
//     * @return 影响行数
//     */
//    int insert(GiftReceiveEntity giftReceiveEntity);
//
//
//    /**
//     * 新增数据
//     *
//     * @param giftReceiveEntity 实例对象
//     * @return 影响行数
//     */
//    int insertSelective(GiftReceiveEntity giftReceiveEntity);
//
//    /**
//     * 批量新增数据（MyBatis原生foreach方法）
//     *
//     * @param entities List<GiftEntity> 实例对象列表
//     * @return 影响行数
//     */
//    int insertBatch(@Param("entities") List<GiftReceiveEntity> entities);
//
//    /**
//     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
//     *
//     * @param entities List<GiftEntity> 实例对象列表
//     * @return 影响行数
//     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
//     */
//    int insertOrUpdateBatch(@Param("entities") List<GiftReceiveEntity> entities);
//
//    /**
//     * 修改数据
//     *
//     * @param giftReceiveEntity 实例对象
//     * @return 影响行数
//     */
//    int update(GiftReceiveEntity giftReceiveEntity);
//
//    /**
//     * 通过主键删除数据
//     *
//     * @param id 主键
//     * @return 影响行数
//     */
//    int deleteById(Long id);


}

