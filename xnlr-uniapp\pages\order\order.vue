<template>
	<view class="order-container">
		<!-- 订单列表 -->
		<view class="order-list">
			<view class="empty-tip" v-if="orderList.length === 0">
				暂无订单数据
			</view>

			<!-- 订单项 -->
			<view class="order-item" v-for="order in orderList" :key="order.id">
				<view class="order-header">
					<text class="order-no">订单号：{{ order.orderNumber }}</text>
					<text class="order-status">{{ order.status }}</text>
				</view>

				<!-- 礼物订单 -->
				<view class="order-content" v-if="order.giftId">
					<image :src="order.giftUrl" class="goods-image" mode="aspectFill"></image>
					<view class="goods-info">
						<view class="goods-name-box">
							<text class="goods-type">礼物</text>
							<text class="goods-name">{{ order.giftName }}</text>
						</view>
						<view class="goods-detail">
							<text class="goods-quantity">数量：{{ order.quantity }}</text>
							<text class="goods-price">{{ order.totalPrice }}虚拟币</text>
						</view>
					</view>
				</view>

				<!-- 服务订单 -->
				<view class="order-content" v-else-if="order.serviceId">
					<view class="service-icon">
						<text class="iconfont">服</text>
					</view>
					<view class="goods-info">
						<view class="goods-name-box">
							<text class="goods-type">服务</text>
							<text class="goods-name">{{ order.serviceTypeName }}</text>
						</view>
						<view class="goods-detail">
							<text class="goods-time">时长：{{ order.serviceTime }}</text>
							<text class="goods-quantity">数量：{{ order.quantity }}</text>
							<text class="goods-price">{{ order.totalPrice }}虚拟币</text>
						</view>
					</view>
				</view>

				<view class="order-seller">
					<text class="seller-label">服务方：</text>
					<text class="seller-name">{{ order.sellerName }}</text>
				</view>

				<view class="order-message" v-if="order.message">
					<text class="message-label">留言：</text>
					<text class="message-content">{{ order.message }}</text>
				</view>

				<view class="order-footer">
					<text class="order-time">{{ order.paymentTime }}</text>
					<view class="order-actions">
						<button class="action-btn contact">联系TA</button>
						<button class="action-btn evaluate" v-if="canEvaluate(order)">评价</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getGiftOrderList } from '@/api/giftOrder'
import { onLoad } from '@dcloudio/uni-app'

// 订单列表数据
const orderList = ref([])

// 加载订单数据
onLoad((options) => {
	console.log('options', options.userId)
	let order = {
		buyerId: options.userId || 0 // 如果没有传userId，默认使用0（当前用户）
	}
	getGiftOrderList(order).then(res => {
		console.log('res', res)
		orderList.value = res
	}).catch(err => {
		console.error('获取订单列表失败', err)
		uni.showToast({
			title: '获取订单列表失败',
			icon: 'none'
		})
	})
})

// 判断是否可以评价
const canEvaluate = (order) => {
	// 这里可以根据业务需求设置评价条件
	// 例如：订单状态为已完成且未评价
	return order.status === '已支付' && !order.hasEvaluated
}

// 处理联系卖家
const handleContact = (order) => {
	// 实现联系卖家的逻辑
	console.log('联系卖家', order.sellerName)
	// 可以跳转到聊天页面或者复制微信号等
}

// 处理评价
const handleEvaluate = (order) => {
	// 实现评价的逻辑
	console.log('评价订单', order.id)
	// 可以跳转到评价页面
}
</script>

<style lang="scss" scoped>
.order-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 20rpx;

	.order-list {
		.empty-tip {
			text-align: center;
			color: #999;
			padding: 100rpx 0;
			font-size: 28rpx;
		}

		.order-item {
			background-color: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

			.order-header {
				display: flex;
				justify-content: space-between;
				margin-bottom: 20rpx;
				padding-bottom: 20rpx;
				border-bottom: 1rpx solid #f0f0f0;

				.order-no {
					font-size: 26rpx;
					color: #666;
				}

				.order-status {
					font-size: 26rpx;
					color: #00d9ff;
					font-weight: 500;
				}
			}

			.order-content {
				display: flex;
				margin-bottom: 20rpx;
				padding-bottom: 20rpx;
				border-bottom: 1rpx solid #f0f0f0;

				.goods-image,
				.service-icon {
					width: 120rpx;
					height: 120rpx;
					border-radius: 10rpx;
					margin-right: 20rpx;
					background-color: #f5f5f5;
					display: flex;
					align-items: center;
					justify-content: center;
					overflow: hidden;
				}

				.service-icon {
					background-color: #e6f7ff;

					.iconfont {
						font-size: 40rpx;
						color: #00d9ff;
					}
				}

				.goods-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.goods-name-box {
						margin-bottom: 10rpx;

						.goods-type {
							display: inline-block;
							padding: 4rpx 12rpx;
							background-color: #f0f8ff;
							color: #00d9ff;
							font-size: 22rpx;
							border-radius: 6rpx;
							margin-right: 10rpx;
						}

						.goods-name {
							font-size: 28rpx;
							color: #333;
							font-weight: 500;
						}
					}

					.goods-detail {
						display: flex;
						flex-wrap: wrap;
						align-items: center;

						.goods-time,
						.goods-quantity {
							font-size: 24rpx;
							color: #666;
							margin-right: 20rpx;
						}

						.goods-price {
							font-size: 30rpx;
							color: #ff6b6b;
							font-weight: bold;
							margin-left: auto;
						}
					}
				}
			}

			.order-seller {
				display: flex;
				align-items: center;
				margin-bottom: 15rpx;

				.seller-label {
					font-size: 26rpx;
					color: #666;
				}

				.seller-name {
					font-size: 26rpx;
					color: #333;
				}
			}

			.order-message {
				background-color: #f9f9f9;
				padding: 15rpx;
				border-radius: 8rpx;
				margin-bottom: 20rpx;

				.message-label {
					font-size: 26rpx;
					color: #666;
				}

				.message-content {
					font-size: 26rpx;
					color: #333;
				}
			}

			.order-footer {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.order-time {
					font-size: 24rpx;
					color: #999;
				}

				.order-actions {
					display: flex;
					gap: 20rpx;

					.action-btn {
						font-size: 24rpx;
						padding: 10rpx 30rpx;
						border-radius: 30rpx;
						background-color: #fff;

						&.contact {
							border: 1rpx solid #00d9ff;
							color: #00d9ff;
						}

						&.evaluate {
							background-color: #00d9ff;
							color: #fff;
						}
					}
				}
			}
		}
	}
}
</style>
