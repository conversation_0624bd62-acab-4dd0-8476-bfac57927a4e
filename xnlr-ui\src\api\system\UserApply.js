import request from '@/utils/request'

// 查询申请管理列表
export function listUserApply(query) {
  return request({
    url: '/system/UserApply/list',
    method: 'get',
    params: query
  })
}

// 查询申请管理详细
export function getUserApply(userApplyId) {
  return request({
    url: '/system/UserApply/' + userApplyId,
    method: 'get'
  })
}

// 新增申请管理
export function addUserApply(data) {
  return request({
    url: '/system/UserApply',
    method: 'post',
    data: data
  })
}

// 修改申请管理
export function updateUserApply(data) {
  return request({
    url: '/system/UserApply',
    method: 'put',
    data: data
  })
}

// 删除申请管理
export function delUserApply(userApplyId) {
  return request({
    url: '/system/UserApply/' + userApplyId,
    method: 'delete'
  })
}

// 同意申请
export function approveUserApply(userApplyId) {
  return request({
    url: '/system/UserApply/' + userApplyId + '/approve',
    method: 'put'
  })
}

// 拒绝申请
export function rejectUserApply(userApplyId) {
  return request({
    url: '/system/UserApply/' + userApplyId + '/reject',
    method: 'put'
  })
}
