<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
      <profile name="Annotation profile for ruoyi-admin" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="E:/soft/apache-maven-3.9.8-bin/apache-maven-3.9.8/mvn_resp/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar" />
          <entry name="E:/soft/apache-maven-3.9.8-bin/apache-maven-3.9.8/mvn_resp/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar" />
        </processorPath>
        <module name="ruoyi-admin" />
      </profile>
      <profile name="Annotation profile for system" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="E:/soft/apache-maven-3.9.8-bin/apache-maven-3.9.8/mvn_resp/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar" />
        </processorPath>
        <module name="ruoyi-common" />
        <module name="ruoyi-framework" />
        <module name="ruoyi-generator" />
        <module name="ruoyi-system" />
        <module name="system" />
        <module name="ruoyi-quartz" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="system" options="-parameters" />
    </option>
  </component>
</project>