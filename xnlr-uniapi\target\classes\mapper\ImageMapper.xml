<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.ImageMapper">
    <resultMap id="BaseResultMap" type="com.mycom.system.domain.ImageEntity">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_List">
        id,
        user_id,
        url
    </sql>
    <!--条件查询-->
    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_List"/>
        from image
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
        </where>
    </select>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_List"/>
        from image
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select id,
               user_id,
               url


        from xnlr.image
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into image(user_id, url)
        values
        (#{userId}, #{url})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update image
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="url != null and url != ''">
                url = #{url},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from image where id = #{id}
    </delete>
</mapper>

