<template>
	<view class="staff-apply">
		<view class="apply-header">
			<view class="apply-title">新店员申请资料</view>
			<view class="apply-desc">
				<text>请认真填写资料，审核人员会在3天内添加你的微信，请耐心等候~</text>
			</view>
			<view class="apply-warning">
				<text>未成年人及中学生请不要申请</text>
			</view>
		</view>

		<view class="apply-form">
			<view class="form-card">
				<!-- 昵称 -->
				<view class="form-item">
					<text class="form-label">昵称</text>
					<input type="text" v-model="formData.nickName" placeholder="请输入昵称" class="form-input"
						maxlength="20" />
				</view>

				<!-- 性别 -->
				<view class="form-item">
					<text class="form-label">性别</text>
					<view class="gender-options">
						<view class="gender-option" :class="{ active: formData.gender === 1 }"
							@tap="formData.gender = 1">
							<radio :checked="formData.gender === 1" color="#ff87c3" />
							<text>男</text>
						</view>
						<view class="gender-option" :class="{ active: formData.gender === 0 }"
							@tap="formData.gender = 0">
							<radio :checked="formData.gender === 0" color="#ff87c3" />
							<text>女</text>
						</view>
					</view>
				</view>

				<!-- 年龄 -->
				<view class="form-item">
					<text class="form-label">年龄</text>
					<input type="number" v-model="formData.age" placeholder="请输入年龄" class="form-input" />
				</view>

				<!-- 微信号 -->
				<view class="form-item">
					<text class="form-label">微信</text>
					<input type="text" v-model="formData.wechat" placeholder="请输入微信号" class="form-input" />
				</view>

				<!-- 手机号 -->
				<view class="form-item">
					<text class="form-label">手机号</text>
					<input type="number" v-model="formData.phone" placeholder="请输入手机号" class="form-input"
						maxlength="11" />
				</view>

				<!-- 所在城市 -->
				<view class="form-item">
					<text class="form-label">所在城市</text>
					<view class="city-select" @tap="showCityPicker">
						<text>{{ formData.city || '请选择所在城市' }}</text>
						<image src="@/static/iconfont/right.png" class="arrow-icon"></image>
					</view>
				</view>

				<!-- 相关经验 -->
				<view class="form-item">
					<text class="form-label">相关经验</text>
					<input type="text" v-model="formData.experience" placeholder="是否有其他店铺的经验" class="form-input" />
				</view>

				<!-- 录音 -->
				<view class="form-item">
					<view class="form-label-box">
						<text class="form-label">录音</text>
						<view class="upload-btn" @tap="handleUpload">上传</view>
					</view>
					<view class="record-section">
						<view class="record-button" @touchstart="startRecord" @touchend="stopRecord">
							<view class="mic-circle">
								<image src="@/static/iconfont/mic.svg" class="mic-icon"></image>
							</view>
							<text>{{ recordStatus }}</text>
							<text v-if="recordTime > 0" class="record-time">{{ recordTime }}s</text>
						</view>
					</view>
					<view v-if="formData.voiceFile" class="voice-preview">
						<view class="voice-info">
							<text class="duration">{{ formData.voiceTime }}s</text>
							<view class="voice-actions">
								<image src="@/static/iconfont/play.svg" class="action-icon" @tap="playVoice"
									v-if="!isPlaying"></image>
								<image src="@/static/iconfont/pause.svg" class="action-icon" @tap="stopPlay" v-else>
								</image>
								<image src="@/static/iconfont/delete.svg" class="action-icon" @tap="deleteVoice">
								</image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<button class="submit-btn" :class="{ 'can-submit': canSubmit }" @tap="handleSubmit">
			<text>提交申请</text>
		</button>

		<!-- 城市选择弹窗 -->
		<view class="city-picker-mask" v-if="showCityPickerModal" @tap="closeCityPicker"></view>
		<view class="city-picker-modal" v-if="showCityPickerModal" @tap.stop>
			<view class="picker-header">
				<text class="picker-title">选择省份</text>
				<image src="@/static/iconfont/close.png" class="close-icon" @tap="closeCityPicker"></image>
			</view>
			<view class="picker-content">
				<scroll-view class="province-list full-width" scroll-y>
					<view class="province-item" v-for="(province, index) in cityData" :key="index"
						:class="{ active: currentProvinceIndex === index }" @tap="selectProvince(province)">
						{{ province.name }}
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { insertUserApply } from '@/api/userApply'
import { BASE_DEVURL } from '@/config/index'

const formData = ref({
	nickName: '',
	gender: null,
	age: '',
	wechat: '',
	phone: '',
	city: '',
	experience: '',
	voiceFile: '',
	voiceTime: 0
})

// 城市选择相关
const showCityPickerModal = ref(false)
const currentProvinceIndex = ref(0)

// 示例数据，只保留省份
const cityData = ref([
	{ name: '北京' },
	{ name: '上海' },
	{ name: '天津' },
	{ name: '重庆' },
	{ name: '河北' },
	{ name: '山西' },
	{ name: '辽宁' },
	{ name: '吉林' },
	{ name: '黑龙江' },
	{ name: '江苏' },
	{ name: '浙江' },
	{ name: '安徽' },
	{ name: '福建' },
	{ name: '江西' },
	{ name: '山东' },
	{ name: '河南' },
	{ name: '湖北' },
	{ name: '湖南' },
	{ name: '广东' },
	{ name: '海南' },
	{ name: '四川' },
	{ name: '贵州' },
	{ name: '云南' },
	{ name: '陕西' },
	{ name: '甘肃' },
	{ name: '青海' },
	{ name: '内蒙古' },
	{ name: '广西' },
	{ name: '西藏' },
	{ name: '宁夏' },
	{ name: '新疆' },
])

// 显示城市选择器
const showCityPicker = () => {
	showCityPickerModal.value = true
}

// 关闭城市选择器
const closeCityPicker = () => {
	showCityPickerModal.value = false
}

// 选择省份
const selectProvince = (province) => {
	formData.value.city = province.name
	closeCityPicker()
}

// 处理录音
const recordStatus = ref('按住开始录音')
const recordTime = ref(0)
const isPlaying = ref(false)
let recordTimer = null

// 初始化录音管理器
const recorderManager = uni.getRecorderManager()
const innerAudioContext = uni.createInnerAudioContext()

// 录音相关事件处理
recorderManager.onStart(() => {
	recordStatus.value = '松开结束录音'
	recordTime.value = 0
	recordTimer = setInterval(() => {
		recordTime.value++
	}, 1000)
})

recorderManager.onStop((res) => {
	clearInterval(recordTimer)
	recordStatus.value = '按住开始录音'

	if (recordTime.value < 1) {
		uni.showToast({
			title: '录音时间太短',
			icon: 'none'
		})
		return
	}

	formData.value.voiceFile = res.tempFilePath
	formData.value.voiceTime = recordTime.value
	recordTime.value = 0
})

// 开始录音
const startRecord = () => {
	recorderManager.start({
		duration: 60000, // 最长录音时间，单位ms
		sampleRate: 16000, // 采样率
		numberOfChannels: 1, // 录音通道数
		encodeBitRate: 96000, // 编码码率
		format: 'mp3' // 音频格式
	})
}

// 停止录音
const stopRecord = () => {
	recorderManager.stop()
}

// 播放录音
const playVoice = () => {
	if (!formData.value.voiceFile) return

	isPlaying.value = true
	innerAudioContext.src = formData.value.voiceFile
	innerAudioContext.play()

	innerAudioContext.onEnded(() => {
		isPlaying.value = false
	})
}

// 停止播放
const stopPlay = () => {
	innerAudioContext.stop()
	isPlaying.value = false
}

// 删除录音
const deleteVoice = () => {
	formData.value.voiceFile = ''
	formData.value.voiceTime = 0
}

// 组件卸载时清理
onUnmounted(() => {
	clearInterval(recordTimer)
	innerAudioContext.destroy()
})

// 处理上传
const handleUpload = () => {
	uni.chooseMessageFile({
		count: 1,
		type: 'file',
		extension: ['.mp3', '.aac'],
		success: (res) => {
			const tempFile = res.tempFiles[0]

			// 检查文件大小（限制为10MB）
			if (tempFile.size > 10 * 1024 * 1024) {
				uni.showToast({
					title: '音频文件过大，请选择10MB以内的文件',
					icon: 'none'
				})
				return
			}

			// 检查文件类型
			const fileExt = tempFile.name.substring(tempFile.name.lastIndexOf('.')).toLowerCase()
			if (!['.mp3', '.aac'].includes(fileExt)) {
				uni.showToast({
					title: '请选择mp3或aac格式的音频文件',
					icon: 'none'
				})
				return
			}

			// 创建音频对象获取时长
			const audio = uni.createInnerAudioContext()
			audio.src = tempFile.path

			audio.onCanplay(() => {
				// 获取音频时长（向上取整）
				const duration = Math.ceil(audio.duration)

				// 检查音频时长（限制为60秒）
				if (duration > 60) {
					uni.showToast({
						title: '音频时长不能超过60秒',
						icon: 'none'
					})
					audio.destroy()
					return
				}

				// 更新表单数据
				formData.value.voiceFile = tempFile.path
				formData.value.voiceTime = duration

				uni.showToast({
					title: '上传成功',
					icon: 'success'
				})

				audio.destroy()
			})

			audio.onError(() => {
				uni.showToast({
					title: '音频文件无法播放，请选择其他文件',
					icon: 'none'
				})
				audio.destroy()
			})
		},
		fail: () => {
			uni.showToast({
				title: '选择文件失败',
				icon: 'none'
			})
		}
	})
}

// 提交申请
const handleSubmit = () => {
	if (!canSubmit.value) {
		uni.showToast({
			title: '请填写完整信息',
			icon: 'none'
		})
		return
	}

	// 处理录音文件
	if (formData.value.voiceFile) {
		uni.uploadFile({
			url: BASE_DEVURL + '/userApply/insert',
			filePath: formData.value.voiceFile,
			name: 'voiceFile',
			formData: {
				nickName: formData.value.nickName,
				age: formData.value.age,
				sex: formData.value.gender,
				wechat: formData.value.wechat,
				phone: formData.value.phone,
				city: formData.value.city,
				experience: formData.value.experience || '',
				voiceTime: formData.value.voiceTime,
				applyType: '店员申请'
			},
			header: {
				'Authorization': 'Bearer ' + uni.getStorageSync('token') || ''
			},
			success: (res) => {
				const data = JSON.parse(res.data)
				if (data.code === 200) {
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					})
					uni.navigateBack()
				} else {
					uni.showToast({
						title: data.msg || '提交失败',
						icon: 'none'
					})
				}
			},
			fail: () => {
				uni.showToast({
					title: '提交失败',
					icon: 'none'
				})
			}
		})
	} else {
		// 无文件上传时的处理
		insertUserApply({
			...formData.value,
			experience: formData.value.experience || ''
		}).then(res => {
			uni.showToast({
				title: '提交成功',
				icon: 'success'
			})
			uni.navigateBack()
		})
	}
}

// 判断是否可以提交
const canSubmit = computed(() => {
	return formData.value.nickName &&
		formData.value.gender &&
		formData.value.age &&
		formData.value.wechat &&
		formData.value.phone &&
		formData.value.city &&
		formData.value.voiceFile
})
</script>

<style lang="scss" scoped>
.staff-apply {
	padding: 0;
	min-height: 100vh;
	background: #f8f8f8;

	.apply-header {
		padding: 40rpx 30rpx;
		background: linear-gradient(180deg, #ff87c3 0%, #ffa6d3 100%);
		border-radius: 0 0 30rpx 30rpx;
		margin-bottom: 30rpx;

		.apply-title {
			font-size: 40rpx;
			font-weight: 600;
			color: #fff;
			margin-bottom: 20rpx;
			text-align: center;
		}

		.apply-desc {
			display: flex;
			align-items: center;
			gap: 10rpx;
			margin-bottom: 10rpx;

			text {
				font-size: 32rpx;
				color: #fff;
				line-height: 1.5;
			}
		}

		.apply-warning {
			display: flex;
			align-items: center;
			gap: 10rpx;

			text {
				font-size: 26rpx;
				color: #fff;
			}
		}
	}

	.apply-form {
		padding: 0 30rpx;

		.form-card {
			background: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		}

		.form-item {
			margin-bottom: 30rpx;

			.form-label-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;

				.form-label {
					font-size: 28rpx;
					color: #333;
					display: block;
				}

				.upload-btn {
					font-size: 26rpx;
					color: #ff87c3;
					padding: 8rpx 24rpx;
					background: #fff;
					border: 1px solid #ff87c3;
					border-radius: 30rpx;

					&:active {
						background: #fff5f9;
					}
				}
			}

			.form-input {
				width: 95%;
				height: 80rpx;
				background: #f5f7fa;
				border-radius: 12rpx;
				padding: 0 20rpx;
				font-size: 28rpx;
				transition: all 0.3s;

				&:focus {
					background: #fff;
					box-shadow: 0 0 0 2rpx rgba(255, 135, 195, 0.3);
				}
			}

			.mic-icon {
				width: 32rpx;
				height: 32rpx;
			}

			.gender-options {
				display: flex;
				gap: 40rpx;

				.gender-option {
					display: flex;
					align-items: center;
					gap: 10rpx;
					padding: 20rpx 40rpx;
					background: #f5f7fa;
					border-radius: 12rpx;
					transition: all 0.3s;

					&.active {
						background: #ffe6f2;
					}

					text {
						font-size: 28rpx;
						color: #333;
					}
				}
			}

			.city-select {
				height: 80rpx;
				background: #f5f7fa;
				border-radius: 12rpx;
				padding: 0 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.arrow-icon {
					width: 32rpx;
					height: 32rpx;
				}
			}

			.record-section {
				.record-button {
					height: 230rpx;
					background: #f5f7fa;
					border-radius: 12rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					gap: 20rpx;
					transition: all 0.3s;

					.mic-circle {
						width: 100rpx;
						height: 100rpx;
						background: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						box-shadow: 0 4rpx 12rpx rgba(255, 135, 195, 0.2);

						.mic-icon {
							width: 48rpx;
							height: 48rpx;
						}
					}

					text {
						font-size: 26rpx;
						color: #666;
					}

					&:active {
						background: #ffe6f2;

						.mic-circle {
							transform: scale(0.95);
						}
					}

					.record-time {
						font-size: 24rpx;
						color: #ff87c3;
						margin-top: -10rpx;
					}
				}
			}
		}
	}

	.submit-btn {
		width: calc(100% - 60rpx);
		height: 88rpx;
		line-height: 88rpx;
		background: #ddd;
		color: #fff;
		font-size: 32rpx;
		border-radius: 44rpx;
		margin: 60rpx auto;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10rpx;
		transition: all 0.3s;

		&.can-submit {
			background: linear-gradient(90deg, #ff87c3 0%, #ffa6d3 100%);

			&:active {
				transform: scale(0.98);
			}
		}

		.submit-icon {
			width: 36rpx;
			height: 36rpx;
		}
	}

	/* 城市选择器样式 */
	.city-picker-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 999;
	}

	.city-picker-modal {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 40vh;
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
		z-index: 1000;

		.picker-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #eee;

			.picker-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}

			.close-icon {
				width: 40rpx;
				height: 40rpx;
				padding: 10rpx;
			}
		}

		.picker-content {
			height: calc(70vh - 92rpx);

			.province-list {
				width: 100%;
				height: 100%;
				background: #f8f8f8;

				.province-item {
					padding: 30rpx;
					font-size: 28rpx;
					color: #333;
					text-align: center;
					border-bottom: 1rpx solid #f5f5f5;

					&.active {
						background: #fff;
						color: #ff87c3;
					}

					&:active {
						background: #ffe6f2;
					}
				}
			}
		}
	}

	.voice-preview {
		margin-top: 20rpx;

		.voice-info {
			background: #f5f7fa;
			border-radius: 12rpx;
			padding: 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.duration {
				font-size: 26rpx;
				color: #666;
			}

			.voice-actions {
				display: flex;
				gap: 30rpx;

				.action-icon {
					width: 40rpx;
					height: 40rpx;
					padding: 10rpx;

					&:active {
						opacity: 0.7;
					}
				}
			}
		}
	}
}
</style>
