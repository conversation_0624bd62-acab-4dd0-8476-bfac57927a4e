package com.mycom.system.service.impl;

import com.mycom.system.common.constant.FileEnum;
import com.mycom.system.common.utils.FileUtils;
import com.mycom.system.common.utils.SecurityUtils;
import com.mycom.system.domain.UserEntity;
import com.mycom.system.domain.UserForm;
import com.mycom.system.mapper.UserMapper;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户表(User)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-20 20:31:01
 */
@Service("userService")
public class UserService {
    @Resource
    private UserMapper userMapper;

    /**
     * 通过条件筛选，分页查询
     *
     * @param userForm 查询条件
     * @return 多条数据
     */
    public PageInfo<UserEntity> getList(UserForm userForm) {
        PageHelper.startPage(userForm.getPageNum(), userForm.getPageSize());
        List<UserEntity> list = userMapper.getList(userForm);
        return new PageInfo<>(list);
    }

    public String uploadAvatar(MultipartFile file) {
        String url = FileUtils.uploadFile(file, FileEnum.AVATAR);
//        Long userId = SecurityUtils.getUserId();
//        UserEntity userEntity = new UserEntity();
//        userEntity.setUserId(userId);
//        userEntity.setAvatar(url);
//        userMapper.update(userEntity);
        return url;
    }

    /**
     * 通过ID查询单条数据
     *
     * @return 实例对象
     */
    public UserEntity queryById(Long userId) {
        if (userId == 0) {
            userId = SecurityUtils.getUserId();
        }
        return userMapper.selectByPrimaryKey(userId);
    }

    /**
     * 新增数据
     *
     * @param userEntity 实例对象
     * @return 实例对象
     */
    public UserEntity insert(UserEntity userEntity) {
        userMapper.insert(userEntity);
        return userEntity;
    }

    /**
     * 修改数据
     *
     * @param userEntity 实例对象
     * @return 实例对象
     */
    public UserEntity update(UserEntity userEntity) {
        userMapper.update(userEntity);
        return queryById(userEntity.getUserId());
    }

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 是否成功
     */
    public boolean deleteById(Long userId) {
        return userMapper.deleteById(userId) > 0;
    }

    public UserEntity getUserByOpenId(String openid) {
        return userMapper.getUserByOpenId(openid);
    }

    public UserEntity selectUserByUserName(String username) {
        return userMapper.selectUserByUserName(username);
    }


}

