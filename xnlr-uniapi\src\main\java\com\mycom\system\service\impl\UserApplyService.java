package com.mycom.system.service.impl;

import com.mycom.system.common.constant.FileEnum;
import com.mycom.system.common.utils.FileUtils;
import com.mycom.system.common.utils.SecurityUtils;
import com.mycom.system.domain.UserApplyEntity;
import com.mycom.system.mapper.UserApplyMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-08 18:08:41
 */
@Service("userApplyService")
public class UserApplyService {
    @Resource
    private UserApplyMapper userApplyMapper;

    /**
     * 条件筛选，分页查询
     *
     * @param userApplyEntity
     */
    public PageInfo<UserApplyEntity> getListPage(UserApplyEntity userApplyEntity) {
        PageHelper.startPage(userApplyEntity.getPageNum(), userApplyEntity.
                getPageSize());
        List<UserApplyEntity> list = userApplyMapper.getList(userApplyEntity);
        return new PageInfo<>(list);
    }

    /**
     * 条件筛选，查询全部
     *
     * @param userApplyEntity
     */
    public List<UserApplyEntity> getList(UserApplyEntity userApplyEntity) {
        List<UserApplyEntity> list = userApplyMapper.getList(userApplyEntity);
        return list;
    }

    /**
     * 通过ID查询
     *
     * @param userApplyId
     */
    public UserApplyEntity queryById(Long userApplyId) {
        return userApplyMapper.selectByPrimaryKey(userApplyId);
    }

    /**
     * 插入
     *
     * @param userApplyEntity
     */
    public void insert(UserApplyEntity userApplyEntity) {
        if (ObjectUtils.isNotEmpty(userApplyEntity.getVoiceFile())) {
            // 上传录音文件
            String url = FileUtils.uploadFile(userApplyEntity.getVoiceFile(), FileEnum.VOICE);
            userApplyEntity.setVoice(url);
        }
        Long userId = SecurityUtils.getUserId();
        userApplyEntity.setStatus("申请中");
        userApplyEntity.setUserId(userId);
        userApplyMapper.insert(userApplyEntity);
    }

    public void insertCommon(UserApplyEntity userApplyEntity) {
        Long userId = SecurityUtils.getUserId();
        userApplyEntity.setStatus("申请中");
        userApplyEntity.setUserId(userId);
        userApplyMapper.insertCommon(userApplyEntity);
    }

    /**
     * 修改
     *
     * @param userApplyEntity
     */
    public UserApplyEntity update(UserApplyEntity userApplyEntity) {
        userApplyMapper.update(userApplyEntity);
        return queryById(userApplyEntity.getUserApplyId());
    }

    /**
     * 删除
     *
     * @param userApplyId 主键
     */
    public boolean deleteById(Long userApplyId) {
        return userApplyMapper.deleteById(userApplyId) > 0;
    }


}

