package com.mycom.system.controller;

import com.mycom.system.domain.ServiceTypeEntity;
import com.mycom.system.service.impl.ServiceTypeService;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.mycom.system.common.vo.Result;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-02-26 10:01:02
 */
@RestController
@RequestMapping("serviceType")
public class ServiceTypeController {

    @Resource
    private ServiceTypeService serviceTypeService;

    /**
     * 条件筛选，分页查询
     *
     * @param serviceTypeEntity
     */
    @PostMapping("list/page")
    public Result<PageInfo<ServiceTypeEntity>> getListPage(@RequestBody ServiceTypeEntity serviceTypeEntity) {
        return Result.success(serviceTypeService.getListPage(serviceTypeEntity));
    }

    /**
     * 条件筛选,查询全部
     *
     * @param serviceTypeEntity
     */
    @PostMapping("list")
    public Result<List<ServiceTypeEntity>> getList(@RequestBody ServiceTypeEntity serviceTypeEntity) {
        return Result.success(serviceTypeService.getList(serviceTypeEntity));
    }


    /**
     * 主键查询单条数据
     *
     * @param id
     */
    @GetMapping("{id}")
    public Result<ServiceTypeEntity> queryById(@PathVariable("id") Long id) {
        return Result.success(serviceTypeService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param serviceTypeEntity
     */
    @PostMapping("/insert")
    public Result<ServiceTypeEntity> add(ServiceTypeEntity serviceTypeEntity) {
        return Result.success(serviceTypeService.insert(serviceTypeEntity));
    }

    /**
     * 编辑数据
     *
     * @param serviceTypeEntity
     */
    @PutMapping("/update")
    public Result<ServiceTypeEntity> edit(ServiceTypeEntity serviceTypeEntity) {
        return Result.success(serviceTypeService.update(serviceTypeEntity));
    }

    /**
     * 删除数据
     *
     * @param id
     */
    @DeleteMapping("/delete")
    public Result<Boolean> deleteById(Long id) {
        return Result.success(serviceTypeService.deleteById(id));
    }

}

