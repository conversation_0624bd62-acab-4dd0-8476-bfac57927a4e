<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserApplyMapper">
    
    <resultMap type="UserApply" id="UserApplyResult">
        <result property="userApplyId"    column="user_apply_id"    />
        <result property="userId"    column="user_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="phone"    column="phone"    />
        <result property="sex"    column="sex"    />
        <result property="age"    column="age"    />
        <result property="voice"    column="voice"    />
        <result property="city"    column="city"    />
        <result property="experience"    column="experience"    />
        <result property="voiceTime"    column="voice_time"    />
        <result property="status"    column="status"    />
        <result property="userType"    column="user_type"    />
        <result property="avatar"    column="avatar"    />
        <result property="wechat"    column="wechat"    />
        <result property="tags"    column="tags"    />
        <result property="signature"    column="signature"    />
        <result property="level"    column="level"    />
        <result property="applyType"    column="apply_type"    />
    </resultMap>

    <sql id="selectUserApplyVo">
        select user_apply_id, user_id, nick_name, phone, sex, age, voice, city, experience, voice_time, status, user_type, avatar,wechat,tags,signature,level,apply_type from user_apply
    </sql>

    <select id="selectUserApplyList" parameterType="UserApply" resultMap="UserApplyResult">
        <include refid="selectUserApplyVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="voice != null  and voice != ''"> and voice = #{voice}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="experience != null  and experience != ''"> and experience = #{experience}</if>
            <if test="voiceTime != null "> and voice_time = #{voiceTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="userType != null "> and user_type = #{userType}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
        </where>
    </select>
    
    <select id="selectUserApplyByUserApplyId" parameterType="Long" resultMap="UserApplyResult">
        <include refid="selectUserApplyVo"/>
        where user_apply_id = #{userApplyId}
    </select>

    <insert id="insertUserApply" parameterType="UserApply" useGeneratedKeys="true" keyProperty="userApplyId">
        insert into user_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="phone != null">phone,</if>
            <if test="sex != null">sex,</if>
            <if test="age != null">age,</if>
            <if test="voice != null">voice,</if>
            <if test="city != null">city,</if>
            <if test="experience != null">experience,</if>
            <if test="voiceTime != null">voice_time,</if>
            <if test="status != null">status,</if>
            <if test="userType != null">user_type,</if>
            <if test="avatar != null">avatar,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="sex != null">#{sex},</if>
            <if test="age != null">#{age},</if>
            <if test="voice != null">#{voice},</if>
            <if test="city != null">#{city},</if>
            <if test="experience != null">#{experience},</if>
            <if test="voiceTime != null">#{voiceTime},</if>
            <if test="status != null">#{status},</if>
            <if test="userType != null">#{userType},</if>
            <if test="avatar != null">#{avatar},</if>
         </trim>
    </insert>

    <update id="updateUserApply" parameterType="UserApply">
        update user_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="age != null">age = #{age},</if>
            <if test="voice != null">voice = #{voice},</if>
            <if test="city != null">city = #{city},</if>
            <if test="experience != null">experience = #{experience},</if>
            <if test="voiceTime != null">voice_time = #{voiceTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
        </trim>
        where user_apply_id = #{userApplyId}
    </update>

    <delete id="deleteUserApplyByUserApplyId" parameterType="Long">
        delete from user_apply where user_apply_id = #{userApplyId}
    </delete>

    <delete id="deleteUserApplyByUserApplyIds" parameterType="String">
        delete from user_apply where user_apply_id in 
        <foreach item="userApplyId" collection="array" open="(" separator="," close=")">
            #{userApplyId}
        </foreach>
    </delete>
</mapper>