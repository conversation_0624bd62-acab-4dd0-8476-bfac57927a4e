package com.mycom.system.service.impl;

import com.mycom.system.domain.GiftEntity;
import com.mycom.system.domain.GiftReceiveEntity;
import com.mycom.system.mapper.GiftReceiveMapper;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;
import java.util.List;

/**
 * (Gift)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-23 09:59:04
 */
@Service
public class GiftReceiveService {
    @Resource
    private GiftReceiveMapper giftReceiveMapper;

    /**
     * 通过条件筛选，分页查询
     *
     * @param giftReceiveEntity 查询条件
     * @return 多条数据
     */
    public PageInfo<GiftReceiveEntity> getListPage(GiftReceiveEntity giftReceiveEntity) {
        PageHelper.startPage(giftReceiveEntity.getPageNum(), giftReceiveEntity.
                getPageSize());
        List<GiftReceiveEntity> list = giftReceiveMapper.getList(giftReceiveEntity);
        return new PageInfo<>(list);
    }

    public List<GiftReceiveEntity> getList(GiftReceiveEntity giftReceiveEntity) {
        List<GiftReceiveEntity> list = giftReceiveMapper.getList(giftReceiveEntity);
        return list;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    public GiftReceiveEntity queryById(Long id) {
        return giftReceiveMapper.selectById(id);
    }
//
//    /**
//     * 新增数据
//     *
//     * @param giftReceiveEntity 实例对象
//     * @return 实例对象
//     */
//    public GiftReceiveEntity insert(GiftReceiveEntity giftReceiveEntity) {
//        giftReceiveMapper.insert(giftReceiveEntity);
//        return giftReceiveEntity;
//    }
//

    /**
     * 修改数据
     *
     * @param giftReceiveEntity 实例对象
     * @return 实例对象
     */
    public GiftReceiveEntity update(GiftReceiveEntity giftReceiveEntity) {
        giftReceiveMapper.update(giftReceiveEntity);
        return queryById(giftReceiveEntity.getId());
    }

    public GiftReceiveEntity selectByUserIdAndGiftId(Long userId, Long giftId) {
        return giftReceiveMapper.selectByUserIdAndGiftId(userId, giftId);
    }

    public void insert(GiftReceiveEntity giftReceiveEntity) {
        giftReceiveMapper.insert(giftReceiveEntity);
    }
//
//    /**
//     * 通过主键删除数据
//     *
//     * @param id 主键
//     * @return 是否成功
//     */
//    public boolean deleteById(Long id) {
//        return giftReceiveMapper.deleteById(id) > 0;
//    }


}

