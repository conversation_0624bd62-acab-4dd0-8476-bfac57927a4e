<template>
  <div class="app-container dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6" v-for="(stat, index) in statsCards" :key="index">
        <el-card
          class="stat-card"
          shadow="hover"
          :style="{
            '--icon-color-start': stat.colorStart,
            '--icon-color-end': stat.colorEnd,
            '--card-accent': stat.colorStart,
          }"
        >
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="24">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
              <div class="time-range-selector">
                <el-radio-group
                  v-model="timeRange"
                  size="small"
                  @change="handleTimeRangeChange"
                >
                  <el-radio-button value="3d">3天</el-radio-button>
                  <el-radio-button value="7d">7天</el-radio-button>
                  <el-radio-button value="15d">15天</el-radio-button>
                  <el-radio-button value="1m">1个月</el-radio-button>
                  <el-radio-button value="3m">3个月</el-radio-button>
                  <el-radio-button value="6m">半年</el-radio-button>
                  <el-radio-button value="1y">一年</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="userTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>订单趋势</span>
              <div class="time-range-selector">
                <el-radio-group
                  v-model="timeRange"
                  size="small"
                  @change="handleTimeRangeChange"
                >
                  <el-radio-button label="3d">3天</el-radio-button>
                  <el-radio-button label="7d">7天</el-radio-button>
                  <el-radio-button label="15d">15天</el-radio-button>
                  <el-radio-button label="1m">1个月</el-radio-button>
                  <el-radio-button label="3m">3个月</el-radio-button>
                  <el-radio-button label="6m">半年</el-radio-button>
                  <el-radio-button label="1y">一年</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="orderTrendChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index">
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";
import {
  User,
  ShoppingCart,
  Money,
  Document,
  UserFilled,
  Service,
} from "@element-plus/icons-vue";
import {
  getDashboardStats,
  getUserTrendData,
  getOrderTrendData,
} from "@/api/system/dashboard";

// 响应式数据
const statsCards = ref([]);
const timeRange = ref("7d"); // 默认7天

// 图表引用
const userTrendChart = ref(null);
const orderTrendChart = ref(null);

// 图表实例
let userChart = null;
let orderChart = null;

// 获取统计数据
const getStats = async () => {
  try {
    const { data } = await getDashboardStats();
    statsCards.value = [
      {
        label: "总用户数",
        value: data.totalUsers || 0,
        icon: User,
        colorStart: "#409EFF",
        colorEnd: "#66B3FF",
      },
      {
        label: "总订单数",
        value: data.totalOrders || 0,
        icon: ShoppingCart,
        colorStart: "#67C23A",
        colorEnd: "#85CE61",
      },
      {
        label: "总收入",
        value: `¥${((data.totalRevenue || 0) / 100).toFixed(2)}`,
        icon: Money,
        colorStart: "#E6A23C",
        colorEnd: "#EEBE77",
      },
      {
        label: "店员数量",
        value: data.staffUsers || 0,
        icon: UserFilled,
        colorStart: "#9C27B0",
        colorEnd: "#BA68C8",
      },
    ];
  } catch (error) {
    console.error("获取统计数据失败:", error);
  }
};

// 初始化用户趋势图表
const initUserTrendChart = async (range = timeRange.value) => {
  try {
    const { data } = await getUserTrendData(range);

    if (!userChart) {
      userChart = echarts.init(userTrendChart.value);
    }

    const dates = data.map((item) => item.date);
    const counts = data.map((item) => item.count);

    const option = {
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(255, 255, 255, 0.95)",
        borderColor: "#e4e7ed",
        borderWidth: 1,
        textStyle: {
          color: "#303133",
        },
        extraCssText:
          "border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);",
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: dates,
        axisLine: {
          lineStyle: {
            color: "#e4e7ed",
          },
        },
        axisLabel: {
          color: "#606266",
        },
      },
      yAxis: {
        type: "value",
        axisLine: {
          lineStyle: {
            color: "#e4e7ed",
          },
        },
        axisLabel: {
          color: "#606266",
        },
        splitLine: {
          lineStyle: {
            color: "#f0f2f5",
          },
        },
      },
      series: [
        {
          data: counts,
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 6,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(102, 126, 234, 0.4)" },
              { offset: 1, color: "rgba(102, 126, 234, 0.05)" },
            ]),
          },
          lineStyle: {
            color: "#667eea",
            width: 3,
          },
          itemStyle: {
            color: "#667eea",
            borderColor: "#fff",
            borderWidth: 2,
          },
        },
      ],
    };

    userChart.setOption(option);
  } catch (error) {
    console.error("获取用户趋势数据失败:", error);
  }
};

// 初始化订单趋势图表
const initOrderTrendChart = async (range = timeRange.value) => {
  try {
    const { data } = await getOrderTrendData(range);

    if (!orderChart) {
      orderChart = echarts.init(orderTrendChart.value);
    }

    const dates = data.map((item) => item.date);
    const orderCounts = data.map((item) => item.orderCount);
    const totalAmounts = data.map((item) =>
      (item.totalAmount / 100).toFixed(2)
    );

    const option = {
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(255, 255, 255, 0.95)",
        borderColor: "#e4e7ed",
        borderWidth: 1,
        textStyle: {
          color: "#303133",
        },
        extraCssText:
          "border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);",
      },
      legend: {
        data: ["订单数量", "订单金额"],
        textStyle: {
          color: "#606266",
        },
        top: 10,
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        top: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: dates,
        axisLine: {
          lineStyle: {
            color: "#e4e7ed",
          },
        },
        axisLabel: {
          color: "#606266",
        },
      },
      yAxis: [
        {
          type: "value",
          name: "订单数量",
          position: "left",
          axisLine: {
            lineStyle: {
              color: "#e4e7ed",
            },
          },
          axisLabel: {
            color: "#606266",
          },
          splitLine: {
            lineStyle: {
              color: "#f0f2f5",
            },
          },
        },
        {
          type: "value",
          name: "金额(元)",
          position: "right",
          axisLine: {
            lineStyle: {
              color: "#e4e7ed",
            },
          },
          axisLabel: {
            color: "#606266",
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: "订单数量",
          type: "line",
          data: orderCounts,
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(132, 250, 176, 0.4)" },
              { offset: 1, color: "rgba(132, 250, 176, 0.05)" },
            ]),
          },
          lineStyle: {
            color: "#84fab0",
            width: 3,
          },
          itemStyle: {
            color: "#84fab0",
            borderColor: "#fff",
            borderWidth: 2,
          },
        },
        {
          name: "订单金额",
          type: "line",
          yAxisIndex: 1,
          data: totalAmounts,
          smooth: true,
          symbol: "circle",
          symbolSize: 8,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(255, 154, 158, 0.4)" },
              { offset: 1, color: "rgba(255, 154, 158, 0.05)" },
            ]),
          },
          lineStyle: {
            color: "#ff9a9e",
            width: 3,
          },
          itemStyle: {
            color: "#ff9a9e",
            borderColor: "#fff",
            borderWidth: 2,
          },
        },
      ],
    };

    orderChart.setOption(option);
  } catch (error) {
    console.error("获取订单趋势数据失败:", error);
  }
};

// 时间范围切换处理
const handleTimeRangeChange = async (range) => {
  timeRange.value = range;
  await initUserTrendChart(range);
  await initOrderTrendChart(range);
};

// 初始化所有数据
const initDashboard = async () => {
  await getStats();

  // 等待DOM更新后初始化图表
  await nextTick();
  initUserTrendChart();
  initOrderTrendChart();
};

// 窗口大小改变时重新调整图表
const handleResize = () => {
  if (userChart) userChart.resize();
  if (orderChart) orderChart.resize();
};

// 组件挂载时初始化
onMounted(() => {
  initDashboard();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (userChart) {
    userChart.dispose();
    userChart = null;
  }
  if (orderChart) {
    orderChart.dispose();
    orderChart = null;
  }
});
</script>

<style scoped lang="scss">
.dashboard {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);

  .stats-cards {
    margin-bottom: 32px;

    .stat-card {
      border-radius: 16px;
      border: 2px solid transparent;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, var(--card-accent), transparent);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 0;
      }

      &:hover {
        transform: translateY(-6px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        border-color: var(--card-accent);

        &::before {
          opacity: 0.05;
        }
      }

      :deep(.el-card__body) {
        position: relative;
        z-index: 1;
        padding: 0;
      }

      .stat-content {
        display: flex;
        align-items: center;
        padding: 24px;

        .stat-icon {
          width: 72px;
          height: 72px;
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          color: white;
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
          position: relative;
          overflow: hidden;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              135deg,
              var(--icon-color-start),
              var(--icon-color-end)
            );
            transition: all 0.3s ease;
          }

          .el-icon {
            position: relative;
            z-index: 1;
          }
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .stat-label {
            font-size: 16px;
            color: #6b7280;
            font-weight: 500;
          }
        }
      }
    }
  }

  .charts-row {
    margin-bottom: 32px;
  }

  .chart-card {
    border-radius: 16px;
    border: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #1a1a1a;
      font-size: 18px;
      padding: 20px 24px 0;

      .time-range-selector {
        :deep(.el-radio-group) {
          .el-radio-button__inner {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
          }

          .el-radio-button:first-child .el-radio-button__inner {
            border-radius: 6px 0 0 6px;
          }

          .el-radio-button:last-child .el-radio-button__inner {
            border-radius: 0 6px 6px 0;
          }

          .el-radio-button__original-radio:checked + .el-radio-button__inner {
            background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
            border-color: #409eff;
            color: #fff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          }
        }
      }
    }

    .chart-container {
      height: 360px;
      width: 100%;
      padding: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard {
    .stats-cards {
      .stat-card {
        .stat-content {
          padding: 20px;

          .stat-icon {
            width: 64px;
            height: 64px;
            margin-right: 16px;
          }

          .stat-info {
            .stat-value {
              font-size: 28px;
            }

            .stat-label {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;

    .stats-cards {
      margin-bottom: 24px;

      .stat-card {
        margin-bottom: 16px;

        .stat-content {
          padding: 16px;

          .stat-icon {
            width: 56px;
            height: 56px;
            margin-right: 12px;
          }

          .stat-info {
            .stat-value {
              font-size: 24px;
            }

            .stat-label {
              font-size: 13px;
            }
          }
        }
      }
    }

    .chart-card {
      .chart-container {
        height: 280px;
      }
    }
  }
}
</style>
