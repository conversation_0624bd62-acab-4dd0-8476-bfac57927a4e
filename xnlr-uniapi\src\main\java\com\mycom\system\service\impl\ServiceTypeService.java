package com.mycom.system.service.impl;

import com.mycom.system.domain.ServiceTypeEntity;
import com.mycom.system.mapper.ServiceTypeMapper;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-26 10:01:02
 */
@Service("serviceTypeService")
public class ServiceTypeService {
    @Resource
    private ServiceTypeMapper serviceTypeMapper;

    /**
     * 条件筛选，分页查询
     *
     * @param serviceTypeEntity
     */
    public PageInfo<ServiceTypeEntity> getListPage(ServiceTypeEntity serviceTypeEntity) {
        PageHelper.startPage(serviceTypeEntity.getPageNum(), serviceTypeEntity.
                getPageSize());
        List<ServiceTypeEntity> list = serviceTypeMapper.getList(serviceTypeEntity);
        return new PageInfo<>(list);
    }

    /**
     * 条件筛选，查询全部
     *
     * @param serviceTypeEntity
     */
    public List<ServiceTypeEntity> getList(ServiceTypeEntity serviceTypeEntity) {
        List<ServiceTypeEntity> list = serviceTypeMapper.getList(serviceTypeEntity);
        return list;
    }

    /**
     * 通过ID查询
     *
     * @param id
     */
    public ServiceTypeEntity queryById(Long id) {
        return serviceTypeMapper.selectByPrimaryKey(id);
    }

    /**
     * 插入
     *
     * @param serviceTypeEntity
     */
    public ServiceTypeEntity insert(ServiceTypeEntity serviceTypeEntity) {
        serviceTypeMapper.insert(serviceTypeEntity);
        return serviceTypeEntity;
    }

    /**
     * 修改
     *
     * @param serviceTypeEntity
     */
    public ServiceTypeEntity update(ServiceTypeEntity serviceTypeEntity) {
        serviceTypeMapper.update(serviceTypeEntity);
        return queryById(serviceTypeEntity.getId());
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    public boolean deleteById(Long id) {
        return serviceTypeMapper.deleteById(id) > 0;
    }
}

