<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.GiftMapper">
    <resultMap id="BaseResultMap" type="com.mycom.system.domain.GiftEntity">
        <!--@Table gift-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="price" column="price" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        url,
        price,
        name
    </sql>
    <!--条件查询-->
    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gift
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
            <if test="price != null">
                and price = #{price}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
        </where>
    </select>
    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gift
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select id,
               url,
               price,
               name


        from xnlr.gift
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from gift
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
            <if test="price != null">
                and price = #{price}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into gift(url, price, name)
        values (#{url}, #{price}, #{name})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into gift
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="url != null and url != ''">
                url,
            </if>
            <if test="price != null">
                price,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="url != null and url != ''">
                #{url},
            </if>
            <if test="price != null">
                #{price},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into gift(url, price, name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.url}, #{entity.price}, #{entity.name})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into gift(url, price, name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.url}, #{entity.price}, #{entity.name})
        </foreach>
        on duplicate key update
        url = values(url),
        price = values(price),
        name = values(name)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update gift
        <set>
            <if test="url != null and url != ''">
                url = #{url},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from gift where id = #{id}
    </delete>
</mapper>

