import request from '@/utils/request'

// 获取首页统计数据
export function getDashboardStats() {
  return request({
    url: '/system/dashboard/stats',
    method: 'get'
  })
}

// 获取用户增长趋势数据
export function getUserTrendData(timeRange = '7d') {
  return request({
    url: '/system/dashboard/userTrend',
    method: 'get',
    params: { timeRange }
  })
}

// 获取订单趋势数据
export function getOrderTrendData(timeRange = '7d') {
  return request({
    url: '/system/dashboard/orderTrend',
    method: 'get',
    params: { timeRange }
  })
}

// 获取服务类型分布数据
export function getServiceDistributionData() {
  return request({
    url: '/system/dashboard/serviceDistribution',
    method: 'get'
  })
}

// 获取最新订单列表
export function getRecentOrders() {
  return request({
    url: '/system/dashboard/recentOrders',
    method: 'get'
  })
}

// 获取待审核申请列表
export function getPendingApplies() {
  return request({
    url: '/system/dashboard/pendingApplies',
    method: 'get'
  })
}
