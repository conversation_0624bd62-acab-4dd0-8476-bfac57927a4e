<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DashboardMapper">

    <!-- 统计总用户数 -->
    <select id="countTotalUsers" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user
    </select>

    <!-- 统计总订单数 -->
    <select id="countTotalOrders" resultType="java.lang.Long">
        SELECT COUNT(*) FROM gift_order
    </select>

    <!-- 统计总收入 -->
    <select id="countTotalRevenue" resultType="java.lang.Long">
        SELECT COALESCE(SUM(total_price), 0) FROM gift_order
    </select>

    <!-- 统计待审核申请数 -->
    <select id="countPendingApplies" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user_apply WHERE status = '申请中'
    </select>

    <!-- 统计在线用户数 -->
    <select id="countOnlineUsers" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user WHERE is_online = 1
    </select>

    <!-- 统计服务提供者数量 -->
    <select id="countServiceProviders" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user WHERE user_type = 1
    </select>

    <!-- 获取用户增长趋势数据 -->
    <select id="getUserTrendData" resultType="java.util.Map">
        SELECT
            DATE(created_at) as date,
            COUNT(*) as count
        FROM user
        WHERE created_at IS NOT NULL
        <choose>
            <when test="timeRange == '3d'">
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
            </when>
            <when test="timeRange == '7d'">
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            </when>
            <when test="timeRange == '15d'">
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
            </when>
            <when test="timeRange == '1m'">
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
            </when>
            <when test="timeRange == '3m'">
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
            </when>
            <when test="timeRange == '6m'">
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
            </when>
            <when test="timeRange == '1y'">
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
            </when>
            <otherwise>
                AND created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            </otherwise>
        </choose>
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    </select>

    <!-- 获取订单趋势数据 -->
    <select id="getOrderTrendData" resultType="java.util.Map">
        SELECT
            DATE(STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s')) as date,
            COUNT(*) as orderCount,
            COALESCE(SUM(total_price), 0) as totalAmount
        FROM gift_order
        WHERE payment_time IS NOT NULL AND payment_time != ''
        <choose>
            <when test="timeRange == '3d'">
                AND STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
            </when>
            <when test="timeRange == '7d'">
                AND STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            </when>
            <when test="timeRange == '15d'">
                AND STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(CURDATE(), INTERVAL 15 DAY)
            </when>
            <when test="timeRange == '1m'">
                AND STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
            </when>
            <when test="timeRange == '3m'">
                AND STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
            </when>
            <when test="timeRange == '6m'">
                AND STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
            </when>
            <when test="timeRange == '1y'">
                AND STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
            </when>
            <otherwise>
                AND STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s') >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            </otherwise>
        </choose>
        GROUP BY DATE(STR_TO_DATE(payment_time, '%Y-%m-%d %H:%i:%s'))
        ORDER BY date ASC
    </select>

    <!-- 获取服务类型分布数据 -->
    <select id="getServiceDistributionData" resultType="java.util.Map">
        SELECT 
            st.service_name as serviceName,
            COUNT(us.id) as count
        FROM service_type st 
        LEFT JOIN user_service us ON st.id = us.service_type_id
        GROUP BY st.id, st.service_name
        ORDER BY count DESC
    </select>

    <!-- 获取最新订单列表（最近10条） -->
    <select id="getRecentOrders" resultType="java.util.Map">
        SELECT 
            go.id,
            go.order_number as orderNumber,
            buyer.nick_name as buyerName,
            seller.nick_name as sellerName,
            go.total_price as totalPrice,
            go.status,
            go.payment_time as paymentTime
        FROM gift_order go
        LEFT JOIN user buyer ON go.buyer_id = buyer.user_id
        LEFT JOIN user seller ON go.seller_id = seller.user_id
        ORDER BY STR_TO_DATE(go.payment_time, '%Y-%m-%d %H:%i:%s') DESC
        LIMIT 10
    </select>

    <!-- 获取待审核申请列表（最近10条） -->
    <select id="getPendingApplies" resultType="java.util.Map">
        SELECT
            user_apply_id as applyId,
            nick_name as nickName,
            phone,
            sex,
            age,
            city,
            status,
            apply_type as applyType
        FROM user_apply
        WHERE status = '申请中'
        ORDER BY user_apply_id DESC
        LIMIT 10
    </select>

    <!-- 统计店员数量 -->
    <select id="countStaffUsers" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user WHERE user_type = 1
    </select>

</mapper>
