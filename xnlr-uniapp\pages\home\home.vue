<template>
	<view class="home-container">
		<!--头部搜索-->
		<view class="home-header-search">
			<view class="home-header-input">
				<image src="@/static/iconfont/search.png" mode="widthFix" class="home-header-input-icon" />
				<input type="text" placeholder="请输入呢称/省份" v-model="searchValue" />
			</view>
			<view class="home-header-btn" @tap="search">
				<view>搜索</view>
			</view>
		</view>
		<!--导航tab-->
		<view class="home-header-tab">
			<view class="home-header-tab-item" @tap="changeTab('online')">
				<view>{{ online }}</view>
				<image src="@/static/iconfont/up.png" v-if="tab === 'online'" />
				<image src="@/static/iconfont/down.png" v-else />
			</view>
			<view class="home-header-tab-item" @tap="changeTab('sex')">
				<view>{{ sex }}</view>
				<image src="@/static/iconfont/up.png" v-if="tab === 'sex'" />
				<image src="@/static/iconfont/down.png" v-else />
			</view>
			<view class="home-header-tab-item" @tap="changeTab('level')">
				<view>{{ level }}</view>
				<image src="@/static/iconfont/up.png" v-if="tab === 'level'" />
				<image src="@/static/iconfont/down.png" v-else />
			</view>
		</view>
		<!--恋人列表-->
		<view class="home-content">
			<view class="home-content-list" v-for="(item) in userList" :key="item.userId">
				<view class="home-content-list-item" @tap="goToDetail(item.userId)">
					<view class="avatar-container">
						<image :src="item.avatar" mode="widthFix" class="home-content-list-item-avatar" />
						<view class="home-content-list-item-content-level" :class="getLevelClass(item)">
							{{ getLevelText(item) }}
						</view>
					</view>
					<view class="home-content-list-item-content">
						<view class="home-content-list-item-content-detail">
							<view class="home-content-list-item-content-nickname">{{ item.nickName }}</view>
							<view class="home-content-list-item-content-point"></view>
							<view class="home-content-list-item-content-isOnline">{{ item.isOnline == true ? '在线' : '离线'
							}}
							</view>
							<view class="home-content-list-item-content-province">{{ item.province }}</view>
						</view>
						<view class="home-content-list-item-content-sex-box">
							<image
								:src="item.sex === 1 ? '../../static/iconfont/boy.png' : '../../static/iconfont/girl.png'"
								mode="widthFix" class="home-content-list-item-content-sex-icon" />
							<view class="home-content-list-item-content-sex" :class="item.sex === 1 ? 'boy' : 'girl'">
								{{ item.age }}
							</view>
						</view>
						<view class="home-content-list-item-content-tags">
							<view v-for="(tag, tagIndex) in getTags(item.tags)" :key="tagIndex" class="tag-item">
								{{ tag }}
							</view>
						</view>
						<view class="home-content-list-item-content-voice" @tap.stop="handleVoice(item)"
							:class="currentPlayingId === item.userId ? 'playing' : ''">
							<text>{{ item.voiceTime }}s</text>
						</view>
					</view>


				</view>
			</view>
			<!-- 加载更多提示 -->
			<view class="loading-more" v-if="hasMore">
				{{ isLoading ? '加载中...' : '下拉加载更多' }}
			</view>
			<view class="no-more" v-else>没有更多数据了</view>
		</view>

		<!-- 修改弹出层结构 -->
		<view class="popup" v-if="showPopup" @tap="closePopup">
			<view class="popup-content" @click.stop :style="popupStyle">
				<view class="popup-item" v-for="(item, index) in currentList" :key="index" @click="selectItem(item)">
					<view :class="isSelected(item) ? 'active' : ''">{{ item.name }}</view>
				</view>
			</view>
		</view>

		<!-- 授权弹窗 -->
		<view class="auth-mask" v-if="showAuthPopup">
			<view class="auth-popup">
				<view class="auth-content">
					<view class="auth-left">
						<text>该网页需要获取个人信息才可使用完整版服务，当前仅可浏览部分内容。</text>
					</view>
					<view class="auth-right" @tap="handleAuth">
						<view class="auth-btn">使用完整服务</view>
						<image src="@/static/iconfont/right.png" class="auth-btn-icon"></image>
					</view>
				</view>
			</view>
		</view>
		<custom-tab-bar></custom-tab-bar>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import CustomTabBar from '@/components/custom-tab-bar/index.vue'
import { loginByWechat } from '@/api/login.js'
import { setStorageSync } from '../../utils/utils'
import { getUserList } from '../..//api/user.js'
import { onReachBottom } from '@dcloudio/uni-app'
// 搜索关键字
const searchValue = ref('')
// 当前tab
const tab = ref('')
const online = ref('在线')
const sex = ref('不限性别')
const level = ref('不限等级')
// 线上列表
const onlineList = ref([
	{
		name: '全部',
		value: '全部'
	},
	{
		name: '在线',
		value: '在线'
	},
	{
		name: '离线',
		value: '离线'
	}
])
// 性别列表
const sexList = ref([
	{
		name: '不限性别',
		value: '不限性别'
	},
	{
		name: '男',
		value: '男'
	},
	{
		name: '女',
		value: '女'
	}
])
// 等级列表
const levelList = ref([
	{
		name: '不限等级',
		value: '不限等级'
	},
	{
		name: '普通',
		value: '普通'
	},
	{
		name: '金牌',
		value: '金牌'
	},
	{
		name: '镇店',
		value: '镇店'
	},
	{
		name: '男女神',
		value: '男女神'
	}
])

// 是否显示弹出层
const showPopup = ref(false)
// 当前选择的列表
const currentList = ref([])

// 计算弹出层样式
const popupStyle = computed(() => {
	const itemWidth = '33.33%' // 因为有3个选项，所以每个选项宽度是33.33%
	const leftPosition = {
		'online': '0',
		'sex': '33.33%',
		'level': '66.66%'
	}

	return {
		width: itemWidth,
		left: leftPosition[tab.value] || '0'
	}
})

// 修改切换tab方法
const changeTab = (value) => {
	if (tab.value === value) {
		// 如果点击的是当前tab，则关闭弹出层
		showPopup.value = false
		tab.value = ''
		return
	}

	tab.value = value
	// 根据tab设置当前列表
	switch (value) {
		case 'online':
			currentList.value = onlineList.value
			break
		case 'sex':
			currentList.value = sexList.value
			break
		case 'level':
			currentList.value = levelList.value
			break
	}
	showPopup.value = true
}

// 关闭弹出层
const closePopup = () => {
	showPopup.value = false
	tab.value = ''
}

// 选择列表项
const selectItem = (item) => {
	switch (tab.value) {
		case 'online':
			online.value = item.name
			break
		case 'sex':
			sex.value = item.name
			break
		case 'level':
			level.value = item.name
			break
	}
	closePopup()
	// 重置分页并重新获取数据
	resetPagination()
	getUserListApp()
}

// 判断是否是当前选中项
const isSelected = (item) => {
	switch (tab.value) {
		case 'online':
			return item.name === online.value
		case 'sex':
			return item.name === sex.value
		case 'level':
			return item.name === level.value
		default:
			return false
	}
}
// 搜索
const search = () => {
	if (searchValue.value) {
		uni.navigateTo({
			url: `../search/search?searchValue=${searchValue.value}`
		})
		searchValue.value = ''
	}
}

// 控制授权弹窗显示
const showAuthPopup = ref(true)

// 处理授权
const handleAuth = () => {
	uni.getUserProfile({
		desc: '用于完善用户资料',
		success: (res) => {
			console.log('获取用户信息成功：', res)
			wxLogin(res.userInfo.avatarUrl, res.userInfo.nickName)
		},
		fail: (err) => {
			console.log('获取用户信息失败：', err)
			uni.showToast({
				title: '获取用户信息失败',
				icon: 'none'
			})
		}
	})
}

// 微信登录
const wxLogin = (avatarUrl, nickName) => {
	uni.login({
		provider: 'weixin',
		success: async (loginRes) => {
			try {
				const code = loginRes.code;
				const LoginFormWx = {
					code: code,
					avatar: avatarUrl,
					nickName: nickName
				}
				console.log('准备发送登录请求:', LoginFormWx)
				// 使用 await 等待请求结果
				const res = await loginByWechat(LoginFormWx)
				console.log('登录响应:', res)

				if (res.access_token) {
					setStorageSync('token', res.access_token)
					showAuthPopup.value = false
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})
				} else {
					throw new Error('登录失败：没有收到token')
				}
			} catch (err) {
				console.error('登录错误:', err)
				uni.showToast({
					title: '登录失败',
					icon: 'none'
				})
			}
		},
		fail: (err) => {
			console.error('微信登录失败:', err)
			uni.showToast({
				title: '登录失败',
				icon: 'none'
			})
		}
	})
}

// 检查登录状态
const checkLoginStatus = () => {
	// 这里检查本地存储的登录状态
	const token = uni.getStorageSync('token')
	if (token) {
		showAuthPopup.value = false
	}
}
const userList = ref([])

// 修改音频相关的变量定义
const currentPlayingId = ref(null)
const audioContext = ref(null)

// 重写handleVoice函数
// 修改后的音频处理逻辑
const handleVoice = (item) => {
	if (!item.voice) return;

	// 如果点击的是当前播放中的音频
	if (currentPlayingId.value === item.userId) {
		if (audioContext.value) {
			audioContext.value.stop(); // 微信小程序使用 stop() 方法
			audioContext.value = null;
		}
		currentPlayingId.value = null;
		return;
	}

	// 停止并清理之前的音频实例
	if (audioContext.value) {
		audioContext.value.stop();
		audioContext.value = null;
		currentPlayingId.value = null;
	}

	// 创建新音频实例
	const audio = uni.createInnerAudioContext();
	audioContext.value = audio;
	currentPlayingId.value = item.userId;

	// 配置音频参数
	audio.src = item.voice;
	audio.obeyMuteSwitch = false; // 遵循静音开关设置

	// 事件处理
	audio.onPlay(() => {
		console.log('开始播放:', item.userId);
	});

	audio.onEnded(() => {
		console.log('播放结束:', item.userId);
		audioContext.value = null;
		currentPlayingId.value = null;
	});

	audio.onError((err) => {
		console.error('播放错误:', err);
		audioContext.value = null;
		currentPlayingId.value = null;
		uni.showToast({
			title: '音频播放失败',
			icon: 'none'
		});
	});

	// 开始播放（移除了 catch）
	try {
		audio.play();
	} catch (err) {
		console.error('播放失败:', err);
		audioContext.value = null;
		currentPlayingId.value = null;
	}
};


// 分页相关变量
const pageNum = ref(1)
const pageSize = ref(5)
const total = ref(0)
const isLoading = ref(false)
const hasMore = ref(true)
// 获取用户列表
const getUserListApp = async (isLoadMore = false) => {
	if (isLoading.value) return
	isLoading.value = true

	try {
		const res = await getUserList({
			isOnline: online.value === '全部' ? null : online.value === '在线' ? 1 : 0,
			sex: sex.value === '不限性别' ? null : sex.value === '男' ? 1 : 0,
			level: level.value === '不限等级' ? null : level.value,
			pageNum: pageNum.value,
			pageSize: pageSize.value
		})

		// 更新总数
		total.value = res.total

		// 判断是否还有更多数据
		hasMore.value = pageNum.value * pageSize.value < total.value

		// 如果是加载更多，则追加数据，否则替换数据
		if (isLoadMore) {
			pageNum.value++
			userList.value = [...userList.value, ...res.list]
		} else {
			userList.value = res.list
		}
	} catch (error) {
		console.error('获取用户列表失败：', error)
		uni.showToast({
			title: '获取数据失败',
			icon: 'none'
		})
	} finally {
		isLoading.value = false
	}
}

// 重置分页数据
const resetPagination = () => {
	pageNum.value = 1
	hasMore.value = true
	userList.value = []
	total.value = 0 // 重置总数
}

// 获取等级对应的样式类
const getLevelClass = (item) => {
	const levelMap = {
		'普通': 'level-normal',
		'金牌': 'level-gold',
		'镇店': 'level-shop',
		'男女神': 'level-god'
	}
	return levelMap[item.level] || 'level-normal'
}

// 获取等级显示文本
const getLevelText = (item) => {
	if (item.level === '男女神') {
		return item.sex === 1 ? '男神' : '女神'
	}
	return item.level
}

// 监听页面滚动到底部
onReachBottom(() => {
	if (hasMore.value && !isLoading.value) {
		pageNum.value++
		getUserListApp(true)
	}
})

// 跳转详情
const goToDetail = (userId) => {
	uni.navigateTo({
		url: `../home-user-detail/home-user-detail?userId=${userId}`
	})
}

// 组件卸载时清理音频资源
onUnmounted(() => {
	if (audioContext.value) {
		audioContext.value.stop()
		audioContext.value = null
	}
	currentPlayingId.value = null
})

onMounted(() => {
	checkLoginStatus()
	getUserListApp()
})

// 处理标签
const getTags = (tags) => {
	if (!tags) return []
	return tags.split(',')
}
</script>

<style lang="scss" scoped>
.home-container {
	.home-header-search {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px;
		background-color: #008cff;
		z-index: 100;

		.home-header-input {
			flex: 1;
			padding: 3px;
			background-color: #fff;
			border-radius: 20px;
			display: flex;
			align-items: center;

			image {
				width: 20px;
				height: 20px;
				margin: 0 5px;
			}

			input {
				width: 100%;
				height: 30px;
			}
		}

		.home-header-btn {
			width: 50px;
			height: 30px;
			text-align: center;
			line-height: 30px;
			color: #fff;
			font-weight: 600;
		}
	}

	.home-header-tab {
		z-index: 100;
		position: fixed;
		top: 56px;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10px;
		background-color: #f3f3f3;
		font-size: small;

		.home-header-tab-item {
			flex: 1;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 20px;
				height: 20px;
				margin-left: 5px;
			}
		}
	}

	.home-content {
		padding: 200rpx 15rpx;
		background-color: #f3f3f3;
		min-height: 60vh;

		.home-content-list {
			.home-content-list-item {
				background-color: #ffffff;
				border-radius: 10px;
				padding: 15px;
				margin-bottom: 10px;
				display: flex;

				.avatar-container {
					position: relative;
					// z-index: 0;

					.home-content-list-item-avatar {
						width: 110px;
						height: 110px;
						border-radius: 95px;
						margin-right: 20px;
					}

					.home-content-list-item-content-level {
						position: absolute;
						right: 40rpx;
						bottom: 10rpx;
						padding: 5rpx 15rpx;
						border-radius: 20rpx;
						font-size: 20rpx;
						color: #fff;
						font-weight: 600;

						&.level-normal {
							background-color: #ff95ca;
						}

						&.level-gold {
							background-color: #ffd700;
						}

						&.level-shop {
							background-color: #ff4500;
						}

						&.level-god {
							background: linear-gradient(to right, #ff69b4, #ff1493);
						}
					}
				}

				.home-content-list-item-content {
					flex: 1;
					display: flex;
					flex-direction: column;

					.home-content-list-item-content-detail {
						display: flex;

						.home-content-list-item-content-nickname {
							font-size: 14px;
							font-weight: 600;
						}

						.home-content-list-item-content-point {
							width: 5px;
							height: 5px;
							margin: 7px 5px 0 auto;
							background-color: #55ff00;
							border-radius: 5px;
						}

						.home-content-list-item-content-isOnline {
							margin-right: 10px;
							font-size: 10px;
							color: #454545;
						}

						.home-content-list-item-content-province {
							font-size: 10px;
							color: #454545;
						}
					}

					.home-content-list-item-content-sex-box {
						display: flex;
						align-items: center;
						margin-top: 6px;

						.home-content-list-item-content-sex-icon {
							width: 10px;
							height: 10px;
							margin: 0 10rpx 0 3px;
						}

						.home-content-list-item-content-sex {
							font-size: 12px;
							font-weight: 500;

							&.girl {
								color: #ff87c3;
							}

							&.boy {
								color: #7bc0f9;
							}
						}
					}

					.home-content-list-item-content-tags {
						display: flex;
						flex-wrap: wrap;
						gap: 30rpx;
						margin: 15rpx 0;

						.tag-item {
							padding: 2rpx 20rpx;
							border-radius: 30rpx;
							font-size: 22rpx;
							color: #ff87c3;
							border: 1rpx solid #ff87c3;
							background-color: #fff;
						}
					}

					.home-content-list-item-content-voice {
						color: #fff;
						font-weight: 600;
						font-size: 26rpx;
						text-align: center;
						width: 120rpx;
						padding: 6rpx 15rpx;
						margin-top: 8rpx;
						background-color: #5be4ff;
						border-radius: 35rpx;

						&.playing {
							background-color: #ff6b6b;
							position: relative;

							&::after {
								content: '';
								position: absolute;
								right: 10rpx;
								top: 50%;
								transform: translateY(-50%);
								width: 4rpx;
								height: 16rpx;
								background-color: #fff;
								animation: soundWave 0.5s infinite alternate;
							}

							&::before {
								content: '';
								position: absolute;
								right: 18rpx;
								top: 50%;
								transform: translateY(-50%);
								width: 4rpx;
								height: 10rpx;
								background-color: #fff;
								animation: soundWave 0.5s infinite alternate-reverse;
							}
						}
					}
				}
			}
		}

		.loading-more,
		.no-more {
			text-align: center;
			padding: 30rpx 0;
			font-size: 24rpx;
			color: #999;
		}

		.no-more {
			color: #ccc;
		}
	}
}

.popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 101;

	.popup-content {
		position: absolute;
		top: 100px;
		background-color: #fff;
		padding: 10px 0;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

		.popup-item {
			padding: 10px 20px;
			white-space: nowrap;

			view {
				&.active {
					color: #008cff;
				}
			}

			&:active {
				background-color: #f5f5f5;
			}
		}
	}
}

.auth-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 9999;
	display: flex;
	align-items: flex-end;

	.auth-popup {
		width: 100%;
		background-color: #000000;
		border-radius: 20rpx 20rpx 0 0;
		padding: 30rpx;

		.auth-content {
			display: flex;
			align-items: center;
			padding: 20rpx 0;

			.auth-left {
				flex: 1;
				padding-right: 20rpx;
				font-size: 24rpx;
				color: #cecece;
			}

			.auth-right {
				display: flex;
				align-items: center;

				.auth-btn {
					color: #fff;
					font-size: 28rpx;
					padding: 15rpx 15rpx;
					border-radius: 40rpx;
					border: none;

					&::after {
						border: none;
					}
				}

				.auth-btn-icon {
					width: 20rpx;
					height: 20rpx;
				}
			}
		}
	}
}

// 音频波纹动画
@keyframes soundWave {
	from {
		height: 10rpx;
	}

	to {
		height: 20rpx;
	}
}
</style>
