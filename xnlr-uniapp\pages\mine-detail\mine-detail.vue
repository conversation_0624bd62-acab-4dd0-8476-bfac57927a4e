<template>
	<view class="mine-detail">
		<!-- 基本信息编辑区 -->
		<view class="edit-section">
			<!-- 头像 -->
			<view class="edit-item">
				<text class="item-label">头像</text>
				<view class="avatar-box" @tap="fetchChooseAvatar">
					<image :src="userInfo.avatar" class="avatar" mode="aspectFill"></image>
					<view class="edit-icon">修改</view>
				</view>
			</view>

			<!-- 昵称 -->
			<view class="edit-item">
				<text class="item-label">昵称</text>
				<input type="text" v-model="userInfo.nickName" placeholder="请输入昵称" class="input-box" />
			</view>

			<!-- 余额展示 -->
			<view class="edit-item">
				<text class="item-label">余额</text>
				<text class="value-text">{{ userInfo.balance }}币</text>
			</view>

			<!-- 总金额展示 -->
			<view class="edit-item">
				<text class="item-label">总金额</text>
				<text class="value-text">{{ userInfo.totalAmount }}币</text>
			</view>

			<!-- 非普通用户可见的信息 -->
			<template v-if="userInfo.userType !== 3">
				<!-- 性别 -->
				<view class="edit-item">
					<text class="item-label">性别</text>
					<view class="gender-options">
						<view class="gender-option" :class="{ active: userInfo.sex === 1 }" @tap="userInfo.sex = 1">
							<radio :checked="userInfo.sex === 1" color="#ff87c3" />
							<text>男</text>
						</view>
						<view class="gender-option" :class="{ active: userInfo.sex === 0 }" @tap="userInfo.sex = 0">
							<radio :checked="userInfo.sex === 0" color="#ff87c3" />
							<text>女</text>
						</view>
					</view>
				</view>

				<!-- 年龄 -->
				<view class="edit-item">
					<text class="item-label">年龄</text>
					<input type="number" v-model="userInfo.age" placeholder="请输入年龄" class="input-box" />
				</view>

				<!-- 手机号 -->
				<view class="edit-item">
					<text class="item-label">手机号</text>
					<input type="number" v-model="userInfo.phone" placeholder="请输入手机号" class="input-box"
						maxlength="11" />
				</view>

				<!-- 微信号 -->
				<view class="edit-item">
					<text class="item-label">微信号</text>
					<input type="text" v-model="userInfo.wechat" placeholder="请输入微信号" class="input-box" />
				</view>

				<!-- 所在地区 -->
				<view class="edit-item">
					<text class="item-label">所在地区</text>
					<view class="city-select" @tap="showCityPicker">
						<text>{{ userInfo.province || '请选择所在地区' }}</text>
						<image src="@/static/iconfont/right.png" class="arrow-icon"></image>
					</view>
				</view>

				<!-- 基础价格 -->
				<view class="edit-item">
					<text class="item-label">基础价格</text>
					<input type="number" v-model="userInfo.basePrice" placeholder="请输入基础价格" class="input-box" />
				</view>

				<!-- 个性签名 -->
				<view class="edit-item">
					<text class="item-label">个性签名</text>
					<textarea v-model="userInfo.signature" placeholder="请输入个性签名" class="textarea-box" />
				</view>

				<!-- 标签 -->
				<view class="edit-item">
					<text class="item-label">标签</text>
					<input type="text" v-model="userInfo.tags" placeholder="请输入标签，用逗号分隔" class="input-box" />
				</view>
				<!-- 等级 -->
				<view class="edit-item">
					<text class="item-label">等级</text>
					<input type="text" v-model="userInfo.level" placeholder="请输入等级" class="input-box" />
				</view>

				<!-- 录音 -->
				<view class="edit-item">
					<view class="form-label-box">
						<text class="item-label">录音</text>
						<view class="upload-btn" @tap="handleUpload">上传</view>
					</view>
					<view class="record-section">
						<view class="record-button" @touchstart="startRecord" @touchend="stopRecord">
							<view class="mic-circle">
								<image src="@/static/iconfont/mic.svg" class="mic-icon"></image>
							</view>
							<text>{{ recordStatus }}</text>
							<text v-if="recordTime > 0" class="record-time">{{ recordTime }}s</text>
						</view>
					</view>
					<view v-if="userInfo.voice" class="voice-preview">
						<view class="voice-info">
							<text class="duration">{{ userInfo.voiceTime }}s</text>
							<view class="voice-actions">
								<image src="@/static/iconfont/play.svg" class="action-icon" @tap="playVoice"
									v-if="!isPlaying"></image>
								<image src="@/static/iconfont/pause.svg" class="action-icon" @tap="stopPlay" v-else>
								</image>
								<image src="@/static/iconfont/delete.svg" class="action-icon" @tap="deleteVoice">
								</image>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>

		<!-- 保存按钮 -->
		<view class="save-btn-box">
			<button class="save-btn" @tap="handleSave">提交修改(审核通过后生效)</button>
		</view>

		<!-- 城市选择弹窗 -->
		<view class="city-picker-mask" v-if="showCityPickerModal" @tap="closeCityPicker"></view>
		<view class="city-picker-modal" v-if="showCityPickerModal" @tap.stop>
			<view class="picker-header">
				<text class="picker-title">选择省份</text>
				<image src="@/static/iconfont/close.png" class="close-icon" @tap="closeCityPicker"></image>
			</view>
			<view class="picker-content">
				<scroll-view class="province-list full-width" scroll-y>
					<view class="province-item" v-for="(province, index) in cityData" :key="index"
						:class="{ active: currentProvinceIndex === index }" @tap="selectProvince(province)">
						{{ province.name }}
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { onShow } from '@dcloudio/uni-app'
import { getUserInfo } from '@/api/user'
import { ref, onUnmounted } from 'vue'
import { chooseAvatar } from '@/api/file'
import { insertUserApplyCommon, insertUserApply } from '@/api/userApply'
import { BASE_DEVURL } from '@/config/index'

const userInfo = ref({})

// 获取用户信息
const getUserInfoApp = async () => {
	let userId = 0
	const res = await getUserInfo(userId)
	userInfo.value = res
}

// 选择头像
const fetchChooseAvatar = () => {
	chooseAvatar().then(newAvatarUrl => {
		if (newAvatarUrl) {
			userInfo.value.avatar = newAvatarUrl
		}
	})
}

// 城市选择相关
const showCityPickerModal = ref(false)
const currentProvinceIndex = ref(0)

// 省份数据
const cityData = ref([
	{ name: '北京' }, { name: '上海' }, { name: '天津' },
	{ name: '重庆' }, { name: '河北' }, { name: '山西' },
	{ name: '辽宁' }, { name: '吉林' }, { name: '黑龙江' },
	{ name: '江苏' }, { name: '浙江' }, { name: '安徽' },
	{ name: '福建' }, { name: '江西' }, { name: '山东' },
	{ name: '河南' }, { name: '湖北' }, { name: '湖南' },
	{ name: '广东' }, { name: '海南' }, { name: '四川' },
	{ name: '贵州' }, { name: '云南' }, { name: '陕西' },
	{ name: '甘肃' }, { name: '青海' }, { name: '内蒙古' },
	{ name: '广西' }, { name: '西藏' }, { name: '宁夏' },
	{ name: '新疆' }
])

// 显示城市选择器
const showCityPicker = () => {
	showCityPickerModal.value = true
}

// 关闭城市选择器
const closeCityPicker = () => {
	showCityPickerModal.value = false
}

// 选择省份
const selectProvince = (province) => {
	userInfo.value.province = province.name
	closeCityPicker()
}

// 录音相关
const recordStatus = ref('按住开始录音')
const recordTime = ref(0)
const isPlaying = ref(false)
let recordTimer = null

// 初始化录音管理器
const recorderManager = uni.getRecorderManager()
const innerAudioContext = uni.createInnerAudioContext()

// 录音相关事件处理
recorderManager.onStart(() => {
	recordStatus.value = '松开结束录音'
	recordTime.value = 0
	recordTimer = setInterval(() => {
		recordTime.value++
	}, 1000)
})

recorderManager.onStop((res) => {
	clearInterval(recordTimer)
	recordStatus.value = '按住开始录音'

	if (recordTime.value < 1) {
		uni.showToast({
			title: '录音时间太短',
			icon: 'none'
		})
		return
	}

	userInfo.value.voice = res.tempFilePath
	userInfo.value.voiceTime = recordTime.value
	recordTime.value = 0
})

// 开始录音
const startRecord = () => {
	recorderManager.start({
		duration: 60000,
		sampleRate: 16000,
		numberOfChannels: 1,
		encodeBitRate: 96000,
		format: 'mp3'
	})
}

// 停止录音
const stopRecord = () => {
	recorderManager.stop()
}

// 播放录音
const playVoice = () => {
	if (!userInfo.value.voice) return

	isPlaying.value = true
	innerAudioContext.src = userInfo.value.voice
	innerAudioContext.play()

	innerAudioContext.onEnded(() => {
		isPlaying.value = false
	})
}

// 停止播放
const stopPlay = () => {
	innerAudioContext.stop()
	isPlaying.value = false
}

// 删除录音
const deleteVoice = () => {
	userInfo.value.voice = ''
	userInfo.value.voiceTime = 0
}

// 处理上传
const handleUpload = () => {
	uni.chooseMessageFile({
		count: 1,
		type: 'file',
		extension: ['.mp3', '.wav', '.aac'],
		success: (res) => {
			const tempFile = res.tempFiles[0]

			if (tempFile.size > 10 * 1024 * 1024) {
				uni.showToast({
					title: '音频文件过大，请选择10MB以内的文件',
					icon: 'none'
				})
				return
			}

			const fileExt = tempFile.name.substring(tempFile.name.lastIndexOf('.')).toLowerCase()
			if (!['.mp3', '.wav', '.aac'].includes(fileExt)) {
				uni.showToast({
					title: '请选择mp3、wav或aac格式的音频文件',
					icon: 'none'
				})
				return
			}

			const audio = uni.createInnerAudioContext()
			audio.src = tempFile.path

			audio.onCanplay(() => {
				const duration = Math.ceil(audio.duration)

				if (duration > 60) {
					uni.showToast({
						title: '音频时长不能超过60秒',
						icon: 'none'
					})
					audio.destroy()
					return
				}

				userInfo.value.voice = tempFile.path
				userInfo.value.voiceTime = duration

				audio.destroy()
			})

			audio.onError(() => {
				uni.showToast({
					title: '音频文件无法播放，请选择其他文件',
					icon: 'none'
				})
				audio.destroy()
			})
		}
	})
}

// 组件卸载时清理
onUnmounted(() => {
	clearInterval(recordTimer)
	innerAudioContext.destroy()
})

// 保存修改
const handleSave = () => {
	if (userInfo.value.userType === 3) {
		insertUserApplyCommon({
			avatar: userInfo.value.avatar,
			nickName: userInfo.value.nickName,
			userType: userInfo.value.userType,
			applyType: '用户修改'
		}).then(res => {
			uni.showToast({
				title: '保存成功',
				icon: 'success'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1000)
		})
	} else {
		// 处理录音文件
		if (userInfo.value.voice) {
			uni.uploadFile({
				url: BASE_DEVURL + '/userApply/insert',
				filePath: userInfo.value.voice,
				name: 'voiceFile',
				formData: {
					nickName: userInfo.value.nickName,
					sex: userInfo.value.sex,
					age: userInfo.value.age,
					wechat: userInfo.value.wechat,
					phone: userInfo.value.phone,
					city: userInfo.value.province,
					basePrice: userInfo.value.basePrice,
					signature: userInfo.value.signature || '',
					tags: userInfo.value.tags || '',
					avatar: userInfo.value.avatar,
					voiceTime: userInfo.value.voiceTime,
					userType: userInfo.value.userType,
					applyType: '店员修改',
					level: userInfo.value.level
				},
				header: {
					'Authorization': 'Bearer ' + uni.getStorageSync('token') || ''
				},
				success: (uploadRes) => {
					const data = JSON.parse(uploadRes.data)
					if (data.code === 200) {
						uni.showToast({
							title: '提交成功',
							icon: 'success'
						})
						setTimeout(() => {
							uni.navigateBack()
						}, 1000)
					} else {
						uni.showToast({
							title: data.msg || '提交失败',
							icon: 'none'
						})
					}
				},
				fail: () => {
					uni.showToast({
						title: '提交失败',
						icon: 'none'
					})
				}
			})
		} else {
			// 无录音文件时的提交
			insertUserApply({
				nickName: userInfo.value.nickName,
				sex: userInfo.value.sex,
				age: userInfo.value.age,
				wechat: userInfo.value.wechat,
				phone: userInfo.value.phone,
				province: userInfo.value.province,
				basePrice: userInfo.value.basePrice,
				signature: userInfo.value.signature || '',
				tags: userInfo.value.tags || '',
				avatar: userInfo.value.avatar
			}).then(res => {
				uni.showToast({
					title: '提交成功',
					icon: 'success'
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 1000)
			})
		}
	}
}

onShow(() => {
	getUserInfoApp()
})
</script>

<style lang="scss" scoped>
.mine-detail {
	min-height: 100vh;
	background: #f8f8f8;
	padding-bottom: 120rpx;

	.edit-section {
		background: #fff;
		padding: 20rpx 30rpx;

		.edit-item {
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f5f5f5;

			.item-label {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 20rpx;
				display: block;
			}

			.avatar-box {
				width: 160rpx;
				height: 160rpx;
				position: relative;
				margin: 0 auto;

				.avatar {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}

				.edit-icon {
					position: absolute;
					bottom: -10rpx;
					left: 50%;
					transform: translateX(-50%);
					background: #ff87c3;
					color: #fff;
					font-size: 20rpx;
					padding: 4rpx 12rpx;
					border-radius: 20rpx;
				}
			}

			.input-box {
				width: 100%;
				height: 80rpx;
				background: #f5f7fa;
				border-radius: 12rpx;
				padding: 0 20rpx;
				font-size: 28rpx;
			}

			.textarea-box {
				width: 100%;
				height: 160rpx;
				background: #f5f7fa;
				border-radius: 12rpx;
				padding: 20rpx;
				font-size: 28rpx;
			}

			.value-text {
				font-size: 28rpx;
				color: #666;
			}

			.gender-options {
				display: flex;
				gap: 40rpx;

				.gender-option {
					display: flex;
					align-items: center;
					gap: 10rpx;
					padding: 20rpx 40rpx;
					background: #f5f7fa;
					border-radius: 12rpx;

					&.active {
						background: #ffe6f2;
					}

					text {
						font-size: 28rpx;
						color: #333;
					}
				}
			}

			.city-select {
				height: 80rpx;
				background: #f5f7fa;
				border-radius: 12rpx;
				padding: 0 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.arrow-icon {
					width: 32rpx;
					height: 32rpx;
				}
			}

			.form-label-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;

				.upload-btn {
					font-size: 26rpx;
					color: #ff87c3;
				}
			}

			.record-section {
				.record-button {
					height: 230rpx;
					background: #f5f7fa;
					border-radius: 12rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					gap: 20rpx;
					transition: all 0.3s;

					.mic-circle {
						width: 100rpx;
						height: 100rpx;
						background: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						box-shadow: 0 4rpx 12rpx rgba(255, 135, 195, 0.2);

						.mic-icon {
							width: 48rpx;
							height: 48rpx;
						}
					}

					text {
						font-size: 26rpx;
						color: #666;
					}

					&:active {
						background: #ffe6f2;

						.mic-circle {
							transform: scale(0.95);
						}
					}

					.record-time {
						font-size: 24rpx;
						color: #ff87c3;
						margin-top: -10rpx;
					}
				}
			}

			.voice-preview {
				margin-top: 20rpx;

				.voice-info {
					background: #f5f7fa;
					border-radius: 12rpx;
					padding: 20rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.duration {
						font-size: 26rpx;
						color: #666;
					}

					.voice-actions {
						display: flex;
						gap: 30rpx;

						.action-icon {
							width: 40rpx;
							height: 40rpx;
							padding: 10rpx;

							&:active {
								opacity: 0.7;
							}
						}
					}
				}
			}
		}
	}

	.save-btn-box {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx 30rpx;
		background: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

		.save-btn {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			background: linear-gradient(90deg, #ff87c3 0%, #ffa6d3 100%);
			color: #fff;
			font-size: 30rpx;
			border-radius: 40rpx;

			&:active {
				transform: scale(0.98);
			}
		}
	}

	/* 城市选择器样式 */
	.city-picker-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 999;
	}

	.city-picker-modal {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 40vh;
		background: #fff;
		border-radius: 24rpx 24rpx 0 0;
		z-index: 1000;

		.picker-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #eee;

			.picker-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333;
			}

			.close-icon {
				width: 40rpx;
				height: 40rpx;
				padding: 10rpx;
			}
		}

		.picker-content {
			height: calc(40vh - 92rpx);

			.province-list {
				width: 100%;
				height: 100%;
				background: #f8f8f8;

				.province-item {
					padding: 30rpx;
					font-size: 28rpx;
					color: #333;
					text-align: center;
					border-bottom: 1rpx solid #f5f5f5;

					&.active {
						background: #fff;
						color: #ff87c3;
					}

					&:active {
						background: #ffe6f2;
					}
				}
			}
		}
	}
}
</style>
