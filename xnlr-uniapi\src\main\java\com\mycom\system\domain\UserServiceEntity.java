package com.mycom.system.domain;

import java.io.Serializable;

import lombok.Data;


/**
 * (Service)实体类
 *
 * <AUTHOR>
 * @since 2025-02-23 14:38:54
 */


@Data

public class UserServiceEntity implements Serializable {
    private static final long serialVersionUID = -43627599143468255L;
    /**
     * 服务时长id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 服务类型id
     */
    private Long serviceTypeId;

    /**
     * 服务时间
     */
    private String serviceTime;

    /**
     * 服务价格
     */
    private Integer servicePrice;
    /**
     * 服务类型名称
     */
    private String serviceName;
    /**
     * 服务类型描述
     */
    private String serviceDescription;


    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

}

