package com.mycom.system.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 申请统计数据VO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ApplyStatsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请状态分布
     */
    private List<StatItem> statusStats;

    /**
     * 审核通过率
     */
    private Double approvalRate;

    /**
     * 总申请数
     */
    private Long totalApplies;

    /**
     * 待审核数
     */
    private Long pendingApplies;

    /**
     * 最近申请(5条)
     */
    private List<RecentApplyVO> recentApplies;
}
