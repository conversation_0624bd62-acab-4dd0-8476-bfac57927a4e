package com.mycom.system.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 最近申请VO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class RecentApplyVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    private Long userApplyId;

    /**
     * 申请人昵称
     */
    private String nickName;

    /**
     * 申请人头像
     */
    private String avatar;

    /**
     * 申请状态
     */
    private String status;

    /**
     * 申请类型
     */
    private String applyType;

    /**
     * 申请人性别
     */
    private Integer sex;

    /**
     * 申请人年龄
     */
    private Object age;
}
