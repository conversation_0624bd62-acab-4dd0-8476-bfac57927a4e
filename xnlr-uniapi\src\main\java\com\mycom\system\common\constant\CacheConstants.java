package com.mycom.system.common.constant;

/**
 * 缓存常量信息
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 缓存有效期，默认48 * 60（分钟），用于登录
     */
    public final static long EXPIRATION = 48 * 60;

    /**
     * 缓存刷新时间，默认24 * 60（分钟），用于登录
     */
    public final static long REFRESH_TIME = 24 * 60;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 5;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACK_IP_LIST = SYS_CONFIG_KEY + "sys.login.blackIPList";

    /**
     * 小程序access_token cache key
     */
    public static final String APPLET_ACCESS_TOKEN_KEY = "applet_access_token";

    /**
     * 小程序access_token 有效期，微信7200s，系统保存7000s
     */
    public static final long APPLET_ACCESS_TOKEN_EXPIRES_IN = 7000;

    /**
     * 访问量缓存前缀
     */
    public final static String INTERVIEW_AMOUNT_KEY = "interview_amount:";

    /**
     * 短信验证码 redis key
     */
    public static final String MSG_CAPTCHA_CODE_KEY = "msg_captcha_codes:";
}
