import auth from '@/plugins/auth'
import router, { constantRoutes, dynamicRoutes } from '@/router'
import { getRouters } from '@/api/menu'
import Layout from '@/layout/index'
import ParentView from '@/components/ParentView'
import InnerLink from '@/layout/components/InnerLink'

// 匹配views里面所有的.vue文件
const modules = import.meta.glob('@/views/**/*.vue')
console.log('🚀 Modules loaded:', Object.keys(modules).length, 'files')
console.log('📂 First few modules:', Object.keys(modules).slice(0, 5))

const usePermissionStore = defineStore(
  'permission',
  {
    state: () => ({
      routes: [],
      addRoutes: [],
      defaultRoutes: [],
      topbarRouters: [],
      sidebarRouters: []
    }),
    actions: {
      setRoutes(routes) {
        this.addRoutes = routes
        this.routes = constantRoutes.concat(routes)
      },
      setDefaultRoutes(routes) {
        this.defaultRoutes = constantRoutes.concat(routes)
      },
      setTopbarRoutes(routes) {
        this.topbarRouters = routes
      },
      setSidebarRouters(routes) {
        this.sidebarRouters = routes
      },
      generateRoutes(roles) {
        return new Promise(resolve => {
          // 向后端请求路由数据
          getRouters().then(res => {
            const sdata = JSON.parse(JSON.stringify(res.data))
            const rdata = JSON.parse(JSON.stringify(res.data))
            const defaultData = JSON.parse(JSON.stringify(res.data))
            const sidebarRoutes = filterAsyncRouter(sdata)
            const rewriteRoutes = filterAsyncRouter(rdata, false, true)
            const defaultRoutes = filterAsyncRouter(defaultData)
            const asyncRoutes = filterDynamicRoutes(dynamicRoutes)
            asyncRoutes.forEach(route => { router.addRoute(route) })
            this.setRoutes(rewriteRoutes)
            this.setSidebarRouters(constantRoutes.concat(sidebarRoutes))
            this.setDefaultRoutes(sidebarRoutes)
            this.setTopbarRoutes(defaultRoutes)
            resolve(rewriteRoutes)
          })
        })
      }
    }
  })

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter(route => {
    if (type && route.children) {
      route.children = filterChildren(route.children)
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === 'Layout') {
        route.component = Layout
      } else if (route.component === 'ParentView') {
        route.component = ParentView
      } else if (route.component === 'InnerLink') {
        route.component = InnerLink
      } else {
        route.component = loadView(route.component)
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type)
    } else {
      delete route['children']
      delete route['redirect']
    }
    return true
  })
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === 'ParentView' && !lastRouter) {
        el.children.forEach(c => {
          c.path = el.path + '/' + c.path
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c))
            return
          }
          children.push(c)
        })
        return
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + '/' + el.path
      if (el.children && el.children.length) {
        children = children.concat(filterChildren(el.children, el))
        return
      }
    }
    children = children.concat(el)
  })
  return children
}

// 动态路由遍历，验证是否具备权限
export function filterDynamicRoutes(routes) {
  const res = []
  routes.forEach(route => {
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        res.push(route)
      }
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        res.push(route)
      }
    }
  })
  return res
}

export const loadView = (view) => {
  console.log('🔍 loadView called with view:', view);

  // 如果没有模块，返回一个错误组件
  if (!modules || Object.keys(modules).length === 0) {
    console.error('❌ No modules loaded!');
    return () => import('@/views/error/404.vue');
  }

  let res;
  const moduleKeys = Object.keys(modules);
  console.log('📁 Available modules count:', moduleKeys.length);

  // 尝试多种匹配方式
  const matchPatterns = [
    // 精确匹配：@/views/system/UserApply/index
    `@/views/${view}`,
    // 带.vue扩展名：@/views/system/UserApply/index.vue
    `@/views/${view}.vue`,
    // 不同路径分隔符
    `@/views/${view.replace(/\//g, '\\')}`,
    `@/views/${view.replace(/\//g, '\\')}.vue`
  ];

  for (const pattern of matchPatterns) {
    if (modules[pattern]) {
      console.log('✅ Exact match found:', pattern);
      res = () => modules[pattern]();
      break;
    }
  }

  // 如果精确匹配失败，尝试模糊匹配
  if (!res) {
    console.log('❌ No exact match found, trying fuzzy matching...');
    for (const path in modules) {
      const normalizedPath = path.replace(/\\/g, '/');
      const dir = normalizedPath.replace('@/views/', '').replace('.vue', '');

      if (dir === view || normalizedPath.includes(view)) {
        console.log('🔄 Fuzzy match found:', path, 'for view:', view);
        res = () => modules[path]();
        break;
      }
    }
  }

  if (!res) {
    console.error('💥 No component found for view:', view);
    console.log('📋 All available modules:');
    moduleKeys.slice(0, 10).forEach(path => {
      const normalizedPath = path.replace(/\\/g, '/');
      const dir = normalizedPath.replace('@/views/', '').replace('.vue', '');
      console.log(`  - ${path} -> ${dir}`);
    });

    // 返回一个默认的错误组件而不是undefined
    return () => import('@/views/error/404.vue');
  }

  console.log('🎉 Component function created successfully for:', view);
  return res;
}

export default usePermissionStore
