package com.mycom.system.common.rt;

import com.alibaba.fastjson2.JSONObject;
import com.mycom.system.domain.LoginFormWx;
import com.mycom.system.common.rt.config.AppletApiProperties;
//import com.mycom.system.utils.DateUtils;
//import com.mycom.system.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

@Component
public class WeChatApiRestTemplate {

    private final RestTemplate restTemplate;

    private final AppletApiProperties appletApiProperties;


    public WeChatApiRestTemplate(@Qualifier("weChatClientRestTemplate") RestTemplate restTemplate,
                                 AppletApiProperties appletApiProperties) {
        this.restTemplate = restTemplate;
        this.appletApiProperties = appletApiProperties;
    }

    /**
     * 获取open_id
     */
    public JSONObject loginWeChat(LoginFormWx api) {
        URI url = UriComponentsBuilder
                .fromHttpUrl(appletApiProperties.getUrl() + "/sns/jscode2session")
                .queryParam("appid", appletApiProperties.getAppid())
                .queryParam("secret", appletApiProperties.getSecret())
                .queryParam("js_code", api.getCode())
                .queryParam("grant_type", appletApiProperties.getGrantType())
                .encode().build().toUri();
        return restTemplate.exchange(url, HttpMethod.GET, null, JSONObject.class).getBody();
    }
}
