package com.ruoyi.system.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.system.mapper.DashboardMapper;
import com.ruoyi.system.service.IDashboardService;

/**
 * 首页数据统计服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class DashboardServiceImpl implements IDashboardService {
    private static final Logger log = LoggerFactory.getLogger(DashboardServiceImpl.class);

    @Autowired
    private DashboardMapper dashboardMapper;

    /**
     * 获取首页统计数据
     * 
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取各项统计数据
            Long totalUsers = dashboardMapper.countTotalUsers();
            Long totalOrders = dashboardMapper.countTotalOrders();
            Long totalRevenue = dashboardMapper.countTotalRevenue();
            Long pendingApplies = dashboardMapper.countPendingApplies();
            Long onlineUsers = dashboardMapper.countOnlineUsers();
            Long serviceProviders = dashboardMapper.countServiceProviders();
            Long staffUsers = dashboardMapper.countStaffUsers();

            stats.put("totalUsers", totalUsers != null ? totalUsers : 0L);
            stats.put("totalOrders", totalOrders != null ? totalOrders : 0L);
            stats.put("totalRevenue", totalRevenue != null ? totalRevenue : 0L);
            stats.put("pendingApplies", pendingApplies != null ? pendingApplies : 0L);
            stats.put("onlineUsers", onlineUsers != null ? onlineUsers : 0L);
            stats.put("serviceProviders", serviceProviders != null ? serviceProviders : 0L);
            stats.put("staffUsers", staffUsers != null ? staffUsers : 0L);

            log.info("获取首页统计数据成功: {}", stats);
        } catch (Exception e) {
            log.error("获取首页统计数据失败", e);
            // 返回默认值
            stats.put("totalUsers", 0L);
            stats.put("totalOrders", 0L);
            stats.put("totalRevenue", 0L);
            stats.put("pendingApplies", 0L);
            stats.put("onlineUsers", 0L);
            stats.put("serviceProviders", 0L);
            stats.put("staffUsers", 0L);
        }

        return stats;
    }

    /**
     * 获取用户增长趋势数据
     *
     * @param timeRange 时间范围
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getUserTrendData(String timeRange) {
        try {
            List<Map<String, Object>> trendData = dashboardMapper.getUserTrendData(timeRange);
            log.info("获取用户增长趋势数据成功，时间范围: {}, 数据条数: {}", timeRange, trendData.size());
            return trendData;
        } catch (Exception e) {
            log.error("获取用户增长趋势数据失败，时间范围: {}", timeRange, e);
            return new ArrayList<>(); // 返回空列表
        }
    }

    /**
     * 获取订单趋势数据
     *
     * @param timeRange 时间范围
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getOrderTrendData(String timeRange) {
        try {
            List<Map<String, Object>> trendData = dashboardMapper.getOrderTrendData(timeRange);
            log.info("获取订单趋势数据成功，时间范围: {}, 数据条数: {}", timeRange, trendData.size());
            return trendData;
        } catch (Exception e) {
            log.error("获取订单趋势数据失败，时间范围: {}", timeRange, e);
            return new ArrayList<>(); // 返回空列表
        }
    }

    /**
     * 获取服务类型分布数据
     * 
     * @return 分布数据
     */
    @Override
    public List<Map<String, Object>> getServiceDistributionData() {
        try {
            List<Map<String, Object>> distributionData = dashboardMapper.getServiceDistributionData();
            log.info("获取服务类型分布数据成功，数据条数: {}", distributionData.size());
            return distributionData;
        } catch (Exception e) {
            log.error("获取服务类型分布数据失败", e);
            return new ArrayList<>(); // 返回空列表
        }
    }

    /**
     * 获取最新订单列表
     * 
     * @return 订单列表
     */
    @Override
    public List<Map<String, Object>> getRecentOrders() {
        try {
            List<Map<String, Object>> recentOrders = dashboardMapper.getRecentOrders();
            log.info("获取最新订单列表成功，数据条数: {}", recentOrders.size());
            return recentOrders;
        } catch (Exception e) {
            log.error("获取最新订单列表失败", e);
            return new ArrayList<>(); // 返回空列表
        }
    }

    /**
     * 获取待审核申请列表
     * 
     * @return 申请列表
     */
    @Override
    public List<Map<String, Object>> getPendingApplies() {
        try {
            List<Map<String, Object>> pendingApplies = dashboardMapper.getPendingApplies();
            log.info("获取待审核申请列表成功，数据条数: {}", pendingApplies.size());
            return pendingApplies;
        } catch (Exception e) {
            log.error("获取待审核申请列表失败", e);
            return new ArrayList<>(); // 返回空列表
        }
    }
}
