package com.mycom.system.controller;

import com.mycom.system.domain.LeaveMessageEntity;
import com.mycom.system.service.impl.LeaveMessageService;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.mycom.system.common.vo.Result;

import javax.annotation.Resource;
import java.util.List;

/**
 * (LeaveMessage)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-23 10:19:28
 */
@RestController
@RequestMapping("leaveMessage")
public class LeaveMessageController {
    /**
     * 服务对象
     */
    @Resource
    private LeaveMessageService leaveMessageService;

    /**
     * 通过条件筛选，分页查询
     *
     * @param leaveMessageEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list/page")
    public Result<PageInfo<LeaveMessageEntity>> getListPage(@RequestBody LeaveMessageEntity leaveMessageEntity) {
        return Result.success(leaveMessageService.getListPage(leaveMessageEntity));
    }

    /**
     * 通过条件筛选，分页查询
     *
     * @param leaveMessageEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list")
    public Result<List<LeaveMessageEntity>> getList(@RequestBody LeaveMessageEntity leaveMessageEntity) {
        return Result.success(leaveMessageService.getList(leaveMessageEntity));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键 实体
     * @return 单条数据
     */
    @GetMapping("{id}")
    public Result<LeaveMessageEntity> queryById(@PathVariable("id") Long id) {
        return Result.success(leaveMessageService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param leaveMessageEntity 实体
     * @return 新增结果
     */
    @PostMapping
    public Result<LeaveMessageEntity> add(LeaveMessageEntity leaveMessageEntity) {
        return Result.success(leaveMessageService.insert(leaveMessageEntity));
    }

    /**
     * 编辑数据
     *
     * @param leaveMessageEntity 实体
     * @return 编辑结果
     */
    @PutMapping
    public Result<LeaveMessageEntity> edit(LeaveMessageEntity leaveMessageEntity) {
        return Result.success(leaveMessageService.update(leaveMessageEntity));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public Result<Boolean> deleteById(Long id) {
        return Result.success(leaveMessageService.deleteById(id));
    }

}

