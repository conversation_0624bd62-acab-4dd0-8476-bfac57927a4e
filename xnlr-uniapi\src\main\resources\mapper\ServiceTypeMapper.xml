<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.ServiceTypeMapper">
    <resultMap id="BaseResultMap" type="com.mycom.system.domain.ServiceTypeEntity">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="serviceName" column="service_name" jdbcType="VARCHAR"/>
        <result property="serviceDescription" column="service_description" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_List">
        id,
        service_name,
        service_description
    </sql>
    <!--条件查询-->
    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_List"/>
        from service_type
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="serviceName != null and serviceName != ''">
                and service_name = #{serviceName}
            </if>
            <if test="serviceDescription != null and serviceDescription != ''">
                and service_description = #{serviceDescription}
            </if>
        </where>
    </select>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_List"/>
        from service_type
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select id,
               service_name,
               service_description


        from xnlr.service_type
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into service_type(service_name, service_description)
        values
        (#{serviceName}, #{serviceDescription})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update service_type
        <set>
            <if test="serviceName != null and serviceName != ''">
                service_name = #{serviceName},
            </if>
            <if test="serviceDescription != null and serviceDescription != ''">
                service_description = #{serviceDescription},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from service_type where id = #{id}
    </delete>
</mapper>

