<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.UserApplyMapper">
    <resultMap id="BaseResultMap" type="com.mycom.system.domain.UserApplyEntity">
        <result property="userApplyId" column="user_apply_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="VARCHAR"/>
        <result property="age" column="age" jdbcType="VARCHAR"/>
        <result property="voice" column="voice" jdbcType="VARCHAR"/>
        <result property="voiceTime" column="voice_time" jdbcType="INTEGER"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="experience" column="experience" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="wechat" column="wechat" jdbcType="VARCHAR"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"/>
        <result property="signature" column="signature" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="VARCHAR"/>
        <result property="applyType" column="apply_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_List">
        user_apply_id,
        user_id,
        nick_name,
        phone,
        sex,
        age,
        voice,
        voice_time,
        city,
        experience,
        user_type,
        status,
        avatar,
        wechat,
        tags,
        signature,
        level,
        apply_type
    </sql>
    <!--条件查询-->
    <select id="getList" resultMap="BaseResultMap">
        <include refid="Base_List"/>
        from user_apply
        <where>
            <if test="userApplyId != null">
                and user_apply_id = #{userApplyId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="nickName != null and nickName != ''">
                and nick_name = #{nickName}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="sex != null">
                and sex = #{sex}
            </if>
            <if test="age != null">
                and age = #{age}
            </if>
            <if test="voice != null and voice != ''">
                and voice = #{voice}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="experience != null and experience != ''">
                and experience = #{experience}
            </if>
        </where>
    </select>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_List"/>
        from user_apply
        where user_apply_id = #{userApplyId}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select user_apply_id,
               user_id,
               nick_name,
               phone,
               sex,
               age,
               voice,
               city,
               experience,
               user_type,
               status,
               avatar
        from xnlr.user_apply
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="userApplyId" useGeneratedKeys="true">
        insert into user_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="age != null">
                age,
            </if>
            <if test="voice != null and voice != ''">
                voice,
            </if>
            <if test="city != null and city != ''">
                city,
            </if>
            <if test="experience != null and experience != ''">
                experience,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="voiceTime != null">
                voice_time,
            </if>
            <if test="avatar != null and avatar != ''">
                avatar,
            </if>
            <if test="wechat != null and wechat != ''">
                wechat,
            </if>
            <if test="tags != null and tags != ''">
                tags,
            </if>
            <if test="signature != null and signature != ''">
                signature,
            </if>
            <if test="level != null and level != ''">
                level,
            </if>
            <if test="applyType != null and applyType != ''">
                apply_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="nickName != null and nickName != ''">
                #{nickName},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="sex != null">
                #{sex},
            </if>
            <if test="age != null">
                #{age},
            </if>
            <if test="voice != null and voice != ''">
                #{voice},
            </if>
            <if test="city != null and city != ''">
                #{city},
            </if>
            <if test="experience != null and experience != ''">
                #{experience},
            </if>
            <if test="userType != null">
                #{userType},
            </if>
            <if test="status != null and status != ''">
                #{status},
            </if>
            <if test="voiceTime != null">
                #{voiceTime},
            </if>
            <if test="avatar != null and avatar != ''">
                #{avatar},
            </if>
            <if test="wechat != null and wechat != ''">
                #{wechat},
            </if>
            <if test="tags != null and tags != ''">
                #{tags},
            </if>
            <if test="signature != null and signature != ''">
                #{signature},
            </if>
            <if test="level != null and level != ''">
                #{level},
            </if>
            <if test="applyType != null and applyType != ''">
                #{applyType},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update user_apply
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="sex != null">
                sex = #{sex},
            </if>
            <if test="age != null">
                age = #{age},
            </if>
            <if test="voice != null and voice != ''">
                voice = #{voice},
            </if>
            <if test="city != null and city != ''">
                city = #{city},
            </if>
            <if test="experience != null and experience != ''">
                experience = #{experience},
            </if>
        </set>
        where user_apply_id = #{userApplyId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from user_apply where user_apply_id = #{userApplyId}
    </delete>

    <insert id="insertCommon">
        insert into user_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name,
            </if>
            <if test="avatar != null and avatar != ''">
                avatar,
            </if>
            <if test="applyType != null and applyType != ''">
                apply_type,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="nickName != null and nickName != ''">
                #{nickName},
            </if>
            <if test="avatar != null and avatar != ''">
                #{avatar},
            </if>
            <if test="applyType != null and applyType != ''">
                #{applyType},
            </if>
            <if test="status != null and status != ''">
                #{status},
            </if>
        </trim>
    </insert>
</mapper>

