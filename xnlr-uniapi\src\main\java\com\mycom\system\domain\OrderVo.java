package com.mycom.system.domain;

import lombok.Data;

@Data
public class OrderVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单号
     */
    private String paymentTime;

    /**
     * 订单号
     */
    private String status;

    /**
     * 买家id
     */
    private Long buyerId;

    /**
     * 卖家id
     */
    private Long sellerId;

    /**
     * 礼物id
     */
    private Long giftId;

    /**
     * 服务id
     */
    private Long serviceId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 总价值
     */
    private Integer totalPrice;

    /**
     * 微信号或者其他联系信息
     */
    private String wechat;

    /**
     * 给卖家的留言
     */
    private String message;

    private String giftName;

    private String giftUrl;

    private String serviceTime;

    private String serviceTypeName;

    private String sellerName;


}
