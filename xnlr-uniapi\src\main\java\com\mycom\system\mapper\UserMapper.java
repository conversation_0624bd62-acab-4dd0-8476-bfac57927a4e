package com.mycom.system.mapper;

import com.mycom.system.domain.UserEntity;
import com.mycom.system.domain.UserForm;
import com.mycom.system.domain.vo.StatItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表(user)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-20 20:30:59
 */
@Mapper
public interface UserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    UserEntity selectByPrimaryKey(Long userId);

    /**
     * 统计总行数
     *
     * @param userEntity 查询条件
     * @return 总行数
     */
    long count(UserEntity userEntity);

    /**
     * 新增数据
     *
     * @param userEntity 实例对象
     * @return 影响行数
     */
    int insert(UserEntity userEntity);

    /**
     * 新增数据
     *
     * @param userEntity 实例对象
     * @return 影响行数
     */
    int insertSelective(UserEntity userEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserEntity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<UserEntity> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserEntity> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<UserEntity> entities);

    /**
     * 修改数据
     *
     * @param userEntity 实例对象
     * @return 影响行数
     */
    int update(UserEntity userEntity);

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 影响行数
     */
    int deleteById(Long userId);

    /**
     * 条件查询
     *
     * @param userForm 查询条件
     * @return 对象列表
     */
    List<UserEntity> getList(UserForm userForm);

    UserEntity getUserByOpenId(String openid);

    UserEntity selectUserByUserName(String username);

    // ========== 数据统计相关方法 ==========

    /**
     * 统计总用户数
     */
    Long countTotalUsers();

    /**
     * 统计在线用户数
     */
    Long countOnlineUsers();

    /**
     * 统计服务提供者数量
     */
    Long countServiceProviders();

    /**
     * 统计用户类型分布
     */
    List<StatItem> getUserTypeStats();

    /**
     * 统计性别分布
     */
    List<StatItem> getGenderStats();

    /**
     * 统计在线状态分布
     */
    List<StatItem> getOnlineStats();

    /**
     * 统计地域分布(TOP 10)
     */
    List<StatItem> getProvinceStats();
}
