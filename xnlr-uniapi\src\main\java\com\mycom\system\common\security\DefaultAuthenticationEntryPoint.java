package com.mycom.system.common.security;

import com.alibaba.fastjson.JSON;
import com.mycom.system.common.exception.SystemErrorType;
import com.mycom.system.common.utils.ServletUtils;
import com.mycom.system.common.vo.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * 认证失败处理类 返回未授权
 */
@Component
public class DefaultAuthenticationEntryPoint implements AuthenticationEntryPoint, Serializable {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) {
        logger.error(e.getMessage() + " url:" + request.getRequestURI());
        ServletUtils.renderString(response, JSON.toJSONString(Result.fail(SystemErrorType.GATEWAY_AUTHENTICATION_FAILED)));
    }
}
