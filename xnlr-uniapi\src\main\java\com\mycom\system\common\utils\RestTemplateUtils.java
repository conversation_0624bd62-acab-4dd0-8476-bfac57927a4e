package com.mycom.system.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.springframework.http.HttpRequest;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * RestTemplate工具类
 */
public class RestTemplateUtils {

    private final static List<String> ignoreList = Arrays.asList("/GateSearch/findParkingRecords", "/GateSearch/queryVehicle");

    /**
     * 打印请求日志
     *
     * @param log          日志记录器
     * @param objectMapper 对象映射器
     * @param request      Http请求
     * @param body         http请求体
     * @throws JsonProcessingException 若转换为JSON失败
     */
    public static void printRequest(Logger log, ObjectMapper objectMapper, HttpRequest request, String body) throws JsonProcessingException {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("requestUri", request.getURI().toString());
        map.put("method", request.getMethodValue());
        // map.put("headers", request.getHeaders());
        if (body.length() < 4 * 1024) {
            map.put("body", body);
        } else {
            map.put("body", body.substring(0, 3 * 1024) + "...");
        }
        // 日志过滤
        boolean print = true;
        for (String s : ignoreList) {
            if (request.getURI().toString().contains(s)) {
                print = false;
            }
        }
        if (print) log.debug("\n" + objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(map));
    }

    /**
     * 打印响应日志
     *
     * @param log          日志记录器
     * @param objectMapper 对象映射器
     * @param response     Http响应
     * @param body         Http响应体
     * @throws JsonProcessingException 若转换为JSON失败
     * @throws IOException             若获取http状态码失败
     */
    public static void printResponse(Logger log, ObjectMapper objectMapper, HttpRequest request, ClientHttpResponse response, String body) throws JsonProcessingException, IOException {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("responseUri", request.getURI().toString());
        map.put("statusCode", response.getStatusCode().value());
        map.put("statusText", response.getStatusCode().getReasonPhrase());
        // .put("headers", response.getHeaders());
        if (body.length() < 4 * 1024) {
            map.put("body", body);
        } else {
            map.put("body", body.substring(0, 3 * 1024) + "...");
        }
        boolean print = true;
        for (String s : ignoreList) {
            if (request.getURI().toString().contains(s)) {
                print = false;
            }
        }
        if (print) log.debug("\n" + objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(map));
    }

    /**
     * 保存调用日志
     *
     * @param objectMapper 对象映射器
     * @param request      http请求
     * @param requestBody  http请求体
     * @param response     http响应
     * @param responseBody http响应体
     */
    public static void saveCallLog(ObjectMapper objectMapper, String provider, HttpRequest request, String requestBody, ClientHttpResponse response, String responseBody) throws IOException {
    }

    public static byte[] getResponseBody(ClientHttpResponse response) throws IOException {
        return IOUtils.toByteArray(response.getBody());
    }

    public static Charset getRequestBodyCharset(HttpRequest request) {
        Charset charset = null;
        MediaType contentType = request.getHeaders().getContentType();
        if (contentType != null) {
            charset = request.getHeaders().getContentType().getCharset();
        }
        if (charset != null) {
            return charset;
        }
        return StandardCharsets.UTF_8;
    }

    public static Charset getResponseBodyCharset(ClientHttpResponse response) {
        Charset charset = null;
        MediaType contentType = response.getHeaders().getContentType();
        if (contentType != null) {
            charset = response.getHeaders().getContentType().getCharset();
        }
        if (charset != null) {
            return charset;
        }
        return StandardCharsets.UTF_8;
    }
}
