package com.mycom.system.domain;

import java.io.Serializable;

import lombok.Data;


/**
 * (Gift)实体类
 *
 * <AUTHOR>
 * @since 2025-02-23 09:59:03
 */


@Data

public class GiftReceiveEntity implements Serializable {
    private static final long serialVersionUID = -60340285330021584L;
    /**
     * 接收礼物id
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 礼物id
     */
    private Long giftId;

    /**
     * 礼物数量
     */
    private Integer quantity;

    /**
     * 礼物地址
     */
    private String url;

    /**
     * 礼物价格
     */
    private Integer price;

    /**
     * 礼物名称
     */
    private String name;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

}

