package com.mycom.system.controller;

import com.mycom.system.domain.GiftEntity;
import com.mycom.system.domain.GiftReceiveEntity;
import com.mycom.system.service.impl.GiftReceiveService;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.mycom.system.common.vo.Result;

import javax.annotation.Resource;
import java.util.List;

/**
 * (giftReceive)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-23 09:58:55
 */
@RestController
@RequestMapping("giftReceive")
public class GiftReceiveController {
    /**
     * 服务对象
     */
    @Resource
    private GiftReceiveService giftReceiveService;

    /**
     * 通过条件筛选，分页查询
     *
     * @param giftReceiveEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list/page")
    public Result<PageInfo<GiftReceiveEntity>> getListPage(@RequestBody GiftReceiveEntity giftReceiveEntity) {
        return Result.success(giftReceiveService.getListPage(giftReceiveEntity));
    }

    /**
     * 通过条件筛选,全部
     *
     * @param giftReceiveEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list")
    public Result<List<GiftEntity>> getList(@RequestBody GiftReceiveEntity giftReceiveEntity) {
        return Result.success(giftReceiveService.getList(giftReceiveEntity));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键 实体
     * @return 单条数据
     */
    @GetMapping("{id}")
    public Result<GiftReceiveEntity> queryById(@PathVariable("id") Long id) {
        return Result.success(giftReceiveService.queryById(id));
    }
//
//    /**
//     * 新增数据
//     *
//     * @param giftReceiveEntity 实体
//     * @return 新增结果
//     */
//    @PostMapping
//    public Result<GiftReceiveEntity> add(GiftReceiveEntity giftReceiveEntity) {
//        return Result.success(giftReceiveService.insert(giftReceiveEntity));
//    }
//
//    /**
//     * 编辑数据
//     *
//     * @param giftReceiveEntity 实体
//     * @return 编辑结果
//     */
//    @PutMapping
//    public Result<GiftReceiveEntity> edit(GiftReceiveEntity giftReceiveEntity) {
//        return Result.success(giftReceiveService.update(giftReceiveEntity));
//    }
//
//    /**
//     * 删除数据
//     *
//     * @param id 主键
//     * @return 删除是否成功
//     */
//    @DeleteMapping
//    public Result<Boolean> deleteById(Long id) {
//        return Result.success(giftReceiveService.deleteById(id));
//    }


}

