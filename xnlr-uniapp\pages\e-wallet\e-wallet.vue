<template>
    <view class="wallet-container">
        <!-- 顶部余额卡片 -->
        <view class="balance-card">
            <view class="balance-title">我的余额</view>
            <view class="balance-amount">{{ userInfo.balance || 0 }}<text class="unit">虚拟币</text></view>
            <view class="balance-tips">1虚拟币 = 1人民币</view>
        </view>
        
        <!-- 充值区域 -->
        <view class="recharge-section">
            <view class="section-title">充值金额</view>
            
            <!-- 充值金额选项 -->
            <view class="amount-options">
                <view 
                    class="amount-item" 
                    v-for="(amount, index) in amountOptions" 
                    :key="index"
                    :class="{'active': selectedAmount === amount}"
                    @tap="selectAmount(amount)"
                >
                    {{ amount }}虚拟币
                </view>
                
                <!-- 自定义金额 -->
                <view 
                    class="amount-item custom" 
                    :class="{'active': isCustomAmount}"
                    @tap="enableCustomAmount"
                >
                    自定义
                </view>
            </view>
            
            <!-- 自定义金额输入框 -->
            <view class="custom-amount" v-if="isCustomAmount">
                <input 
                    type="number" 
                    v-model="customAmount" 
                    class="amount-input"
                    placeholder="请输入充值金额"
                    @input="handleCustomAmountInput"
                />
                <text class="input-unit">虚拟币</text>
            </view>
            
            <!-- 实际支付金额 -->
            <view class="payment-info">
                <text class="payment-label">实际支付：</text>
                <text class="payment-amount">¥{{ actualPayment }}</text>
            </view>
            
            <!-- 充值按钮 -->
            <button 
                class="recharge-btn" 
                :disabled="!canRecharge"
                @tap="handleRecharge"
            >
                立即充值
            </button>
        </view>
        
        <!-- 充值记录 -->
        <view class="recharge-records">
            <view class="section-title">充值记录</view>
            
            <view class="record-list">
                <view class="empty-tip" v-if="rechargeRecords.length === 0">
                    暂无充值记录
                </view>
                
                <view 
                    class="record-item" 
                    v-for="(record, index) in rechargeRecords" 
                    :key="index"
                >
                    <view class="record-info">
                        <text class="record-amount">+{{ record.amount }}虚拟币</text>
                        <text class="record-time">{{ record.time }}</text>
                    </view>
                    <text class="record-status" :class="record.status">{{ record.statusText }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getUserInfo } from '@/api/user'

// 用户信息
const userInfo = ref({
    balance: 0
})

// 充值金额选项
const amountOptions = [20, 50, 100, 200, 500,1000]
const selectedAmount = ref(null)
const isCustomAmount = ref(false)
const customAmount = ref('')

// 充值记录
const rechargeRecords = ref([
    // 示例数据，实际应该从API获取
    {
        amount: 100,
        time: '2024-03-15 14:30:25',
        status: 'success',
        statusText: '充值成功'
    },
    {
        amount: 50,
        time: '2024-03-10 09:15:36',
        status: 'success',
        statusText: '充值成功'
    }
])

// 计算实际支付金额
const actualPayment = computed(() => {
    if (isCustomAmount.value && customAmount.value) {
        return customAmount.value
    }
    return selectedAmount.value || 0
})

// 是否可以充值
const canRecharge = computed(() => {
    if (isCustomAmount.value) {
        return customAmount.value && Number(customAmount.value) > 0
    }
    return selectedAmount.value !== null
})

// 选择充值金额
const selectAmount = (amount) => {
    selectedAmount.value = amount
    isCustomAmount.value = false
}

// 启用自定义金额
const enableCustomAmount = () => {
    isCustomAmount.value = true
    selectedAmount.value = null
}

// 处理自定义金额输入
const handleCustomAmountInput = (e) => {
    const value = e.detail.value
    if (value && Number(value) <= 0) {
        customAmount.value = ''
    }
}

// 处理充值
const handleRecharge = () => {
    // 获取充值金额
    const amount = isCustomAmount.value ? Number(customAmount.value) : selectedAmount.value
    
    if (!amount || amount <= 0) {
        uni.showToast({
            title: '请选择或输入有效的充值金额',
            icon: 'none'
        })
        return
    }
    
    // 调用微信支付
    uni.showLoading({
        title: '请求支付中...'
    })
    
    // TODO: 调用后端接口获取支付参数
    // 模拟调用支付接口
    setTimeout(() => {
        uni.hideLoading()
        
        // 调用微信支付API
        uni.requestPayment({
            provider: 'wxpay',
            timeStamp: String(Date.now()),
            nonceStr: 'nonceStr',
            package: 'prepay_id=wx123456789',
            signType: 'MD5',
            paySign: 'paySign',
            success: function(res) {
                console.log('支付成功', res)
                
                // 更新用户余额
                userInfo.value.balance += amount
                
                // 添加充值记录
                rechargeRecords.value.unshift({
                    amount: amount,
                    time: formatDate(new Date()),
                    status: 'success',
                    statusText: '充值成功'
                })
                
                uni.showToast({
                    title: '充值成功',
                    icon: 'success'
                })
                
                // 重置选择
                selectedAmount.value = null
                isCustomAmount.value = false
                customAmount.value = ''
            },
            fail: function(err) {
                console.log('支付失败', err)
                
                // 添加充值记录
                rechargeRecords.value.unshift({
                    amount: amount,
                    time: formatDate(new Date()),
                    status: 'failed',
                    statusText: '充值失败'
                })
                
                uni.showToast({
                    title: '支付失败',
                    icon: 'none'
                })
            }
        })
    }, 1000)
}

// 格式化日期
const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 获取用户信息
onMounted(() => {
    getUserInfo(0).then(res => {
        userInfo.value = res
    })
})
</script>

<style lang="scss" scoped>
.wallet-container {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 30rpx;
    
    .balance-card {
        background: linear-gradient(135deg, #00d9ff, #0080ff);
        border-radius: 20rpx;
        padding: 40rpx;
        color: #fff;
        box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
        margin-bottom: 30rpx;
        
        .balance-title {
            font-size: 28rpx;
            margin-bottom: 20rpx;
        }
        
        .balance-amount {
            font-size: 60rpx;
            font-weight: bold;
            margin-bottom: 10rpx;
            
            .unit {
                font-size: 30rpx;
                font-weight: normal;
                margin-left: 10rpx;
            }
        }
        
        .balance-tips {
            font-size: 24rpx;
            opacity: 0.8;
        }
    }
    
    .recharge-section, .recharge-records {
        background-color: #fff;
        border-radius: 20rpx;
        padding: 30rpx;
        margin-bottom: 30rpx;
        
        .section-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 30rpx;
        }
        
        .amount-options {
            display: flex;
            flex-wrap: wrap;
            gap: 20rpx;
            margin-bottom: 30rpx;
            
            .amount-item {
                width: calc(33.33% - 20rpx);
                height: 80rpx;
                line-height: 80rpx;
                text-align: center;
                background-color: #f5f5f5;
                border-radius: 10rpx;
                font-size: 28rpx;
                color: #333;
                
                &.active {
                    background-color: #e6f7ff;
                    color: #00d9ff;
                    border: 1rpx solid #00d9ff;
                }
                
                &.custom {
                    border: 1rpx dashed #ddd;
                    background-color: transparent;
                }
            }
        }
        
        .custom-amount {
            position: relative;
            margin-bottom: 30rpx;
            
            .amount-input {
                width: 100%;
                height: 80rpx;
                background-color: #f5f5f5;
                border-radius: 10rpx;
                padding: 0 120rpx 0 20rpx;
                font-size: 28rpx;
            }
            
            .input-unit {
                position: absolute;
                right: 20rpx;
                top: 50%;
                transform: translateY(-50%);
                font-size: 28rpx;
                color: #999;
            }
        }
        
        .payment-info {
            text-align: right;
            margin-bottom: 30rpx;
            
            .payment-label {
                font-size: 28rpx;
                color: #666;
            }
            
            .payment-amount {
                font-size: 32rpx;
                color: #ff6b6b;
                font-weight: bold;
            }
        }
        
        .recharge-btn {
            width: 100%;
            height: 90rpx;
            line-height: 90rpx;
            background-color: #00d9ff;
            color: #fff;
            font-size: 32rpx;
            border-radius: 45rpx;
            
            &[disabled] {
                background-color: #ccc;
                color: #fff;
            }
        }
    }
    
    .recharge-records {
        .record-list {
            .empty-tip {
                text-align: center;
                color: #999;
                padding: 50rpx 0;
            }
            
            .record-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 30rpx 0;
                border-bottom: 1rpx solid #eee;
                
                &:last-child {
                    border-bottom: none;
                }
                
                .record-info {
                    display: flex;
                    flex-direction: column;
                    
                    .record-amount {
                        font-size: 30rpx;
                        color: #333;
                        font-weight: 500;
                        margin-bottom: 10rpx;
                    }
                    
                    .record-time {
                        font-size: 24rpx;
                        color: #999;
                    }
                }
                
                .record-status {
                    font-size: 26rpx;
                    
                    &.success {
                        color: #67c23a;
                    }
                    
                    &.failed {
                        color: #f56c6c;
                    }
                    
                    &.pending {
                        color: #e6a23c;
                    }
                }
            }
        }
    }
}
</style>
