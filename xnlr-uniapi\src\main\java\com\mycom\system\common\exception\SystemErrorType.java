package com.mycom.system.common.exception;

import lombok.Getter;

@Getter
public enum SystemErrorType implements ErrorType {

    SYSTEM_ERROR(500, "系统异常"),
    GATEWAY_NOT_FOUND_SERVICE(404, "服务未找到"),
    GATEWAY_AUTHENTICATION_FAILED(401, "认证失败，无法访问系统资源"),
    CHARGE_DEVICE_ERROR_NOT_FOUND(501, "设备异常"),
    CHARGE_DEVICE_ERROR_OFFLINE(502, "设备暂时离线"),
    CHARGE_DEVICE_ERROR_UNPREPARED(503, "设备尚未连接车辆"),
    CHARGE_DEVICE_ERROR_OCCUPY(504, "设备已被占用"),
    CHARGE_DEVICE_ERROR_FAILED(505, "设备故障"),
    CHARGE_DEVICE_ERROR_PREPAID(506, "请先预付款"),
    NO_PLATE_OUT_ZERO(507, "无需支付");

    /**
     * 错误类型码
     */
    private final Integer code;
    /**
     * 错误类型描述信息
     */
    private final String msg;

    SystemErrorType(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
