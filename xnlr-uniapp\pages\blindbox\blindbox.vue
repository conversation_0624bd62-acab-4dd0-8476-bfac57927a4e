<template>
	<view class="blindbox-container">
		<view class="blindbox-box">
			<image src="../../static/iconfont/logo.png" class="blindbox-box-gift" />
			<view class="blindbox-box-title1">1、想要和小哥哥OR小姐姐聊天？</view>
			<view class="blindbox-box-select">
				<view class="blindbox-box-select-item boy" :class="{ 'active-boy': gender === 'boy' }"
					@tap="handleGenderSelect('boy')">
					<image
						:src="gender === 'boy' ? '../../static/iconfont/boy-select.png' : '../../static/iconfont/boy.png'"
						class="blindbox-box-select-item-img" />
					<view class="blindbox-box-select-item-text">小哥哥</view>
				</view>
				<view class="blindbox-box-select-item girl" :class="{ 'active-girl': gender === 'girl' }"
					@tap="handleGenderSelect('girl')">
					<image
						:src="gender === 'girl' ? '../../static/iconfont/girl-select.png' : '../../static/iconfont/girl.png'"
						class="blindbox-box-select-item-img" />
					<view class="blindbox-box-select-item-text">小姐姐</view>
				</view>
			</view>
			<view class="blindbox-box-title2">2、想要和什么样的店员聊天呢？</view>
			<view class="blindbox-box-list">
				<view class="blindbox-box-list-item" v-for="item in levelList" :key="item.value"
					:class="{ 'active-level': level === item.value }" @tap="handleSelect(item)">
					{{ item.name }}
				</view>
				<view class="blindbox-box-list-price">
					{{levelList.find(item => item.value === level).price}}元起
				</view>
				<button class="blindbox-box-list-btn" @tap="handleOrder">立即下单</button>
			</view>
		</view>
	</view>
	<custom-tab-bar></custom-tab-bar>

	<!-- 遮罩层和自定义弹框 -->
	<view class="modal-mask" v-if="showServiceModal"></view>
	<view class="modal-content" v-if="showServiceModal">
		<view class="modal-title">服务类型</view>
		<view class="service-type-box">
			<view class="service-type-item" v-for="item in serviceTypes" :key="item.value"
				:class="{ 'active-service': serviceType === item.value }" @tap="handleServiceTypeSelect(item)">
				{{ item.name }}
			</view>
		</view>

		<view class="modal-title">服务时长</view>
		<view class="duration-box">
			<view class="duration-item" v-for="item in getDurationList" :key="item.value"
				:class="{ 'active-duration': duration === item.value }" @tap="handleDurationSelect(item)">
				{{ item.name }}
			</view>
		</view>

		<view class="modal-title">选择数量</view>
		<view class="quantity-box">
			<view class="quantity-btn" @tap="handleQuantityChange('minus')">-</view>
			<input type="number" v-model="quantity" class="quantity-input" />
			<view class="quantity-btn" @tap="handleQuantityChange('plus')">+</view>
		</view>

		<button class="confirm-btn" :class="{ 'btn-disabled': !isValid }" @tap="handleConfirm">
			确认
		</button>
	</view>
</template>

<script setup>
import CustomTabBar from '@/components/custom-tab-bar/index.vue'
import { ref, computed } from 'vue'

const gender = ref('boy') // 默认选中小哥哥
const level = ref('普通') // 默认选中普通等级

// 等级列表
const levelList = ref([
	{
		name: '普通',
		value: '普通',
		price: 100
	},
	{
		name: '金牌',
		value: '金牌',
		price: 200
	},
	{
		name: '镇店',
		value: '镇店',
		price: 300
	},
	{
		name: '男女神',
		value: '男女神',
		price: 400
	}
])

// 服务弹框
const showServiceModal = ref(false)

// 服务类型
const serviceType = ref('text')

// 服务时长
const duration = ref('')

// 数量
const quantity = ref(1)

// 服务类型列表
const serviceTypes = ref([
	{ name: '文字', value: 'text' },
	{ name: '语音通话', value: 'voice' }
])

// 时长列表
const textDurationList = [
	{ name: '60分钟', value: '60min' },
	{ name: '1天', value: '1day' },
	{ name: '7天', value: '7days' },
	{ name: '30天', value: '30days' }
]

const voiceDurationList = [
	{ name: '30分钟', value: '30min' },
	{ name: '60分钟', value: '60min' }
]

// 根据选择的服务类型获取对应的时长列表
const getDurationList = computed(() => {
	return serviceType.value === 'text' ? textDurationList : voiceDurationList
})

// 验证是否所有必填项都已选择
const isValid = computed(() => {
	return serviceType.value && duration.value && quantity.value > 0
})

// 性别选择处理函数
const handleGenderSelect = (selectedGender) => {
	gender.value = selectedGender
}

// 等级选择处理函数
const handleSelect = (item) => {
	level.value = item.value
}

// 处理立即下单
const handleOrder = () => {
	uni.showModal({
		title: '提示',
		content: '您选择的是【' + level.value + '】等级的【' + (gender.value === 'boy' ? '小哥哥' : '小姐姐') + '】，请确认！',
		success: (res) => {
			if (res.confirm) {
				showServiceModal.value = true
			}
		}
	})
}

// 选择服务类型
const handleServiceTypeSelect = (item) => {
	serviceType.value = item.value
	duration.value = '' // 切换服务类型时重置时长选择
}

// 选择时长
const handleDurationSelect = (item) => {
	duration.value = item.value
}

// 处理数量变化
const handleQuantityChange = (type) => {
	if (type === 'minus' && quantity.value > 1) {
		quantity.value--
	} else if (type === 'plus') {
		quantity.value++
	}
}


// 确认选择
const handleConfirm = () => {
	if (!isValid.value) {
		uni.showToast({
			title: '请完善服务信息',
			icon: 'none'
		})
		return
	}

	console.log('确认订单', {
		gender: gender.value,
		level: level.value,
		serviceType: serviceType.value,
		duration: duration.value,
		quantity: quantity.value
	})

	showServiceModal.value = false
}
</script>

<style lang="scss" scoped>
.blindbox-container {
	background-color: #f3f3f3;
	height: 100vh;
	padding: 20rpx;

	.blindbox-box {
		height: 83vh;
		background-color: #fff;
		padding: 20rpx;
		border-radius: 20rpx;
		text-align: center;

		.blindbox-box-gift {
			width: 200rpx;
			height: 200rpx;
			margin: 0 auto;
		}

		.blindbox-box-title1 {
			font-size: 26rpx;
			margin: 20rpx 0;
		}

		.blindbox-box-select {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 40rpx 100rpx;

			.blindbox-box-select-item {
				width: 180rpx;
				height: 180rpx;
				background-color: #fff;
				border-radius: 180rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				&.active-boy {
					background-color: #7bc0f9;

					.blindbox-box-select-item-text {
						color: #fff;
					}
				}

				&.active-girl {
					background-color: #ff87c3;
					border-color: #ff87c3;

					.blindbox-box-select-item-text {
						color: #fff;
					}
				}

				.blindbox-box-select-item-img {
					width: 60rpx;
					height: 60rpx;
					margin-bottom: 10rpx;
				}

				.blindbox-box-select-item-text {
					font-size: 26rpx;
					margin-top: 0;
				}
			}

			.boy {
				color: #7bc0f9;
				border: 10rpx solid #7bc0f9;
			}

			.girl {
				color: #ff87c3;
				border: 10rpx solid #ff87c3;
			}
		}

		.blindbox-box-title2 {
			font-size: 26rpx;
			margin: 20rpx 0;
		}

		.blindbox-box-list {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			padding: 10rpx 100rpx;

			.blindbox-box-list-item {
				width: 200rpx;
				height: 60rpx;
				line-height: 60rpx;
				background-color: #fff;
				border-radius: 60rpx;
				color: #00aaff;
				margin: 20rpx 0;
				border: 2rpx solid #00aaff;
				font-size: 26rpx;
				transition: all 0.3s ease;

				&.active-level {
					background-color: #00aaff !important;
					color: #fff !important;
					border-color: #00aaff !important;
				}
			}

			.blindbox-box-list-price {
				margin-top: 20rpx;
				padding-left: 180rpx;
				font-size: 35rpx;
				color: #00aaff;
			}

			.blindbox-box-list-btn {
				margin-top: 60rpx;
				width: 100%;
				height: 80rpx;
				line-height: 80rpx;
				background-color: #00aaff;
				color: #fff;
				border-radius: 80rpx;
				font-size: 30rpx;
				font-weight: bold;
				border: none;
			}
		}
	}
}

// 遮罩层样式
.modal-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	z-index: 999;
}

// 弹框内容样式
.modal-content {
	position: fixed;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 500rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	z-index: 1000;

	.modal-title {
		font-size: 28rpx;
		color: #333;
		margin: 20rpx 20rpx 20rpx 20rpx;
		text-align: left;
	}

	.service-type-box {
		display: flex;
		justify-content: space-around;
		margin: 30rpx 0;

		.service-type-item {
			width: 200rpx;
			height: 60rpx;
			line-height: 60rpx;
			background: #fff;
			border: 2rpx solid #00aaff;
			color: #00aaff;
			border-radius: 60rpx;
			text-align: center;
			font-size: 26rpx;
			transition: all 0.3s ease;

			&.active-service {
				background: #00aaff;
				color: #fff;
			}
		}
	}

	.duration-box {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin: 20rpx 20rpx;

		.duration-item {
			width: 200rpx;
			height: 60rpx;
			line-height: 60rpx;
			background: #fff;
			border: 2rpx solid #00aaff;
			color: #00aaff;
			border-radius: 60rpx;
			text-align: center;
			font-size: 26rpx;
			margin: 20rpx 0;
			transition: all 0.3s ease;

			&.active-duration {
				background: #00aaff;
				color: #fff;
			}
		}
	}

	.quantity-box {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 30rpx 0;

		.quantity-btn {
			width: 60rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			background: #f5f5f5;
			border-radius: 10rpx;
			font-size: 40rpx;
			color: #00aaff;
		}

		.quantity-input {
			width: 100rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			margin: 0 20rpx;
			border: 2rpx solid #f5f5f5;
			border-radius: 10rpx;
		}
	}

	.confirm-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background: #00aaff;
		color: #fff;
		border-radius: 80rpx;
		margin-top: 40rpx;
		font-size: 30rpx;

		&.btn-disabled {
			background: #ccc;
		}
	}
}
</style>
