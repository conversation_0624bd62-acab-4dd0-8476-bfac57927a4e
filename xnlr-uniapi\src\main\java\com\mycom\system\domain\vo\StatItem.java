package com.mycom.system.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 统计项VO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class StatItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 统计项名称
     */
    private String name;

    /**
     * 统计项值
     */
    private String value;

    /**
     * 数量
     */
    private Long count;

    /**
     * 百分比
     */
    private Double percentage;

    public StatItem() {}

    public StatItem(String name, String value, Long count, Double percentage) {
        this.name = name;
        this.value = value;
        this.count = count;
        this.percentage = percentage;
    }
}
