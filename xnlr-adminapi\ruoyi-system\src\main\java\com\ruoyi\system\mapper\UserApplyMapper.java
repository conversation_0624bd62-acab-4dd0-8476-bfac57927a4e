package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.UserApply;

/**
 * 申请管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
public interface UserApplyMapper 
{
    /**
     * 查询申请管理
     * 
     * @param userApplyId 申请管理主键
     * @return 申请管理
     */
    public UserApply selectUserApplyByUserApplyId(Long userApplyId);

    /**
     * 查询申请管理列表
     * 
     * @param userApply 申请管理
     * @return 申请管理集合
     */
    public List<UserApply> selectUserApplyList(UserApply userApply);

    /**
     * 新增申请管理
     * 
     * @param userApply 申请管理
     * @return 结果
     */
    public int insertUserApply(UserApply userApply);

    /**
     * 修改申请管理
     * 
     * @param userApply 申请管理
     * @return 结果
     */
    public int updateUserApply(UserApply userApply);

    /**
     * 删除申请管理
     * 
     * @param userApplyId 申请管理主键
     * @return 结果
     */
    public int deleteUserApplyByUserApplyId(Long userApplyId);

    /**
     * 批量删除申请管理
     * 
     * @param userApplyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserApplyByUserApplyIds(Long[] userApplyIds);
}
