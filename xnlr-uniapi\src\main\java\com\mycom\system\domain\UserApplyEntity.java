package com.mycom.system.domain;

import java.io.Serializable;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;


/**
 * (UserApply)实体类
 *
 * <AUTHOR>
 * @since 2025-03-08 18:08:40
 */


@Data

public class UserApplyEntity implements Serializable {
    private static final long serialVersionUID = 130506522502019249L;
    /**
     * 申请表id
     */
    private Long userApplyId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 申请状态
     */
    private String status;

    /**
     * 用户手机号（实时更新）
     */
    private String phone;

    /**
     * 性别 0=女 1-男
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Object age;

    /**
     * 语音url
     */
    private String voice;

    /**
     * 语音时长
     */
    private Integer voiceTime;

    /**
     * 语音url
     */
    private MultipartFile voiceFile;


    /**
     * 所在城市
     */
    private String city;

    /**
     * 经验
     */
    private String experience;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 标签
     */
    private String tags;

    /**
     * 签名
     */
    private String signature;

    /**
     * 等级
     */
    private String level;

    /**
     * 等级
     */
    private String applyType;


    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

}

