package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.MiniUser;
import com.ruoyi.system.service.IMiniUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 店员管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-14
 */
@RestController
@RequestMapping("/system/staff")
public class StaffController extends BaseController {
    @Autowired
    private IMiniUserService miniUserService;

    /**
     * 查询店员列表
     */
    @PreAuthorize("@ss.hasPermi('system:staff:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniUser miniUser) {
        // 只查询店员用户 (user_type = 1)
        miniUser.setUserType(1);
        startPage();
        List<MiniUser> list = miniUserService.selectMiniUserList(miniUser);
        return getDataTable(list);
    }

    /**
     * 导出店员列表
     */
    @PreAuthorize("@ss.hasPermi('system:staff:export')")
    @Log(title = "店员管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniUser miniUser) {
        // 只导出店员用户 (user_type = 1)
        miniUser.setUserType(1);
        List<MiniUser> list = miniUserService.selectMiniUserList(miniUser);
        ExcelUtil<MiniUser> util = new ExcelUtil<MiniUser>(MiniUser.class);
        util.exportExcel(response, list, "店员管理数据");
    }

    /**
     * 获取店员详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:staff:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") String userId) {
        MiniUser user = miniUserService.selectMiniUserByUserId(userId);
        if (user != null && user.getUserType() != 1) {
            return error("该用户不是店员");
        }
        return success(user);
    }

    /**
     * 修改店员信息
     */
    @PreAuthorize("@ss.hasPermi('system:staff:edit')")
    @Log(title = "店员管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniUser miniUser) {
        // 确保用户类型为店员
        miniUser.setUserType(1);
        return toAjax(miniUserService.updateMiniUser(miniUser));
    }

    /**
     * 删除店员
     */
    @PreAuthorize("@ss.hasPermi('system:staff:remove')")
    @Log(title = "店员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable String[] userIds) {
        return toAjax(miniUserService.deleteMiniUserByUserIds(userIds));
    }

    /**
     * 启用/禁用店员
     */
    @PreAuthorize("@ss.hasPermi('system:staff:edit')")
    @Log(title = "店员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{userId}/status")
    public AjaxResult changeStatus(@PathVariable String userId, @RequestBody MiniUser miniUser) {
        miniUser.setUserId(Long.valueOf(userId));
        miniUser.setUserType(1);
        return toAjax(miniUserService.updateMiniUser(miniUser));
    }
}
