package com.mycom.system.mapper;

import com.mycom.system.domain.LeaveMessageEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (leave_message)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-23 10:17:12
 */
public interface LeaveMessageMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    LeaveMessageEntity selectByPrimaryKey(Long id);

    /**
     * 统计总行数
     *
     * @param leaveMessageEntity 查询条件
     * @return 总行数
     */
    long count(LeaveMessageEntity leaveMessageEntity);

    /**
     * 新增数据
     *
     * @param leaveMessageEntity 实例对象
     * @return 影响行数
     */
    int insert(LeaveMessageEntity leaveMessageEntity);


    /**
     * 新增数据
     *
     * @param leaveMessageEntity 实例对象
     * @return 影响行数
     */
    int insertSelective(LeaveMessageEntity leaveMessageEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<LeaveMessageEntity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<LeaveMessageEntity> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<LeaveMessageEntity> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<LeaveMessageEntity> entities);

    /**
     * 修改数据
     *
     * @param leaveMessageEntity 实例对象
     * @return 影响行数
     */
    int update(LeaveMessageEntity leaveMessageEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 条件查询
     *
     * @param leaveMessageEntity 查询条件
     * @return 对象列表
     */
    List<LeaveMessageEntity> getList(LeaveMessageEntity leaveMessageEntity);

}

