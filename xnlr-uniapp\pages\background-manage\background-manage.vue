<template>
  <view class="admin-container">
    <!-- 顶部标题栏 -->
    <view class="admin-header">
      <text class="admin-title">后台管理系统</text>
    </view>

    <!-- 导航菜单 -->
    <view class="admin-nav">
      <view
        class="nav-item"
        v-for="(item, index) in navItems"
        :key="index"
        :class="{ active: currentNav === item.key }"
        @tap="switchNav(item.key)"
      >
        {{ item.name }}
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="admin-content">
      <!-- 用户管理 -->
      <view class="content-section" v-if="currentNav === 'users'">
        <view class="section-header">
          <text class="section-title">用户管理</text>
          <button class="add-btn" @tap="showAddModal('user')">添加用户</button>
        </view>

        <!-- 搜索栏 -->
        <view class="search-bar">
          <input
            type="text"
            v-model="searchKeyword"
            placeholder="搜索用户名/手机号"
            class="search-input"
          />
          <button class="search-btn" @tap="handleSearch">搜索</button>
        </view>

        <!-- 用户列表 -->
        <view class="data-table">
          <view class="table-header">
            <view class="th">ID</view>
            <view class="th">头像</view>
            <view class="th">用户名</view>
            <view class="th">性别/年龄</view>
            <view class="th">地区</view>
            <view class="th">状态</view>
            <view class="th">等级</view>
            <view class="th">余额</view>
            <view class="th">累计充值</view>
            <view class="th">用户类型</view>
            <view class="th">操作</view>
          </view>

          <view
            class="table-row"
            v-for="(user, index) in userList"
            :key="user.id"
          >
            <view class="td">{{ user.id }}</view>
            <view class="td avatar-cell">
              <image
                :src="user.avatar"
                class="user-avatar"
                mode="aspectFill"
              ></image>
            </view>
            <view class="td">{{ user.nickName }}</view>
            <view class="td">{{ user.sex }}/{{ user.age }}</view>
            <view class="td">{{ user.province }}</view>
            <view class="td">{{ user.isOnline }}</view>
            <view class="td">{{ user.level }}</view>
            <view class="td">{{ user.balance }}</view>
            <view class="td">{{ user.totalAmount }}</view>
            <view class="td">{{ getUserType(user.userType) }}</view>
            <view class="td actions">
              <button class="action-btn view" @tap="handleView('user', user)">
                查看
              </button>
              <button class="action-btn edit" @tap="handleEdit('user', user)">
                编辑
              </button>
              <button
                class="action-btn delete"
                @tap="handleDelete('user', user)"
              >
                删除
              </button>
            </view>
          </view>

          <!-- 空数据提示 -->
          <view class="empty-tip" v-if="userList.length === 0 && !loading">
            暂无用户数据
          </view>

          <!-- 加载提示 -->
          <view class="loading-tip" v-if="loading"> 加载中... </view>
        </view>

        <!-- 分页 -->
        <view class="pagination">
          <button
            class="page-btn"
            :disabled="currentPage <= 1"
            @tap="changePage('prev')"
          >
            上一页
          </button>
          <text class="page-info">{{ currentPage }}/{{ totalPages }}</text>
          <button
            class="page-btn"
            :disabled="currentPage >= totalPages"
            @tap="changePage('next')"
          >
            下一页
          </button>
        </view>
      </view>

      <!-- 订单管理 -->
      <view class="content-section" v-if="currentNav === 'orders'">
        <view class="section-header">
          <text class="section-title">订单管理</text>
        </view>

        <!-- 搜索栏 -->
        <view class="search-bar">
          <input
            type="text"
            v-model="searchKeyword"
            placeholder="搜索订单号/用户名"
            class="search-input"
          />
          <button class="search-btn" @tap="handleSearch">搜索</button>
        </view>

        <!-- 订单列表 -->
        <view class="data-table">
          <view class="table-header">
            <view class="th">订单号</view>
            <view class="th">用户</view>
            <view class="th">金额</view>
            <view class="th">状态</view>
            <view class="th">创建时间</view>
            <view class="th">操作</view>
          </view>

          <view
            class="table-row"
            v-for="(order, index) in orderList"
            :key="order.id"
          >
            <view class="td">{{ order.orderNo }}</view>
            <view class="td">{{ order.userName }}</view>
            <view class="td">{{ order.amount }}</view>
            <view class="td">{{ getOrderStatus(order.status) }}</view>
            <view class="td">{{ formatDate(order.createTime) }}</view>
            <view class="td actions">
              <button class="action-btn view" @tap="handleView('order', order)">
                查看
              </button>
              <button class="action-btn edit" @tap="handleEdit('order', order)">
                编辑
              </button>
            </view>
          </view>

          <!-- 空数据提示 -->
          <view class="empty-tip" v-if="orderList.length === 0">
            暂无订单数据
          </view>
        </view>

        <!-- 分页 -->
        <view class="pagination">
          <button
            class="page-btn"
            :disabled="currentPage <= 1"
            @tap="changePage('prev')"
          >
            上一页
          </button>
          <text class="page-info">{{ currentPage }}/{{ totalPages }}</text>
          <button
            class="page-btn"
            :disabled="currentPage >= totalPages"
            @tap="changePage('next')"
          >
            下一页
          </button>
        </view>
      </view>

      <!-- 礼物管理 -->
      <view class="content-section" v-if="currentNav === 'gifts'">
        <view class="section-header">
          <text class="section-title">礼物管理</text>
          <button class="add-btn" @tap="showAddModal('gift')">添加礼物</button>
        </view>

        <!-- 搜索栏 -->
        <view class="search-bar">
          <input
            type="text"
            v-model="searchKeyword"
            placeholder="搜索礼物名称"
            class="search-input"
          />
          <button class="search-btn" @tap="handleSearch">搜索</button>
        </view>

        <!-- 礼物列表 -->
        <view class="gift-grid">
          <view
            class="gift-item"
            v-for="(gift, index) in giftList"
            :key="gift.id"
          >
            <image :src="gift.url" class="gift-image" mode="aspectFit" />
            <view class="gift-info">
              <text class="gift-name">{{ gift.name }}</text>
              <text class="gift-price">{{ gift.price }}虚拟币</text>
            </view>
            <view class="gift-actions">
              <button class="action-btn edit" @tap="handleEdit('gift', gift)">
                编辑
              </button>
              <button
                class="action-btn delete"
                @tap="handleDelete('gift', gift)"
              >
                删除
              </button>
            </view>
          </view>

          <!-- 空数据提示 -->
          <view class="empty-tip gift-empty" v-if="giftList.length === 0">
            暂无礼物数据
          </view>
        </view>

        <!-- 分页 -->
        <view class="pagination">
          <button
            class="page-btn"
            :disabled="currentPage <= 1"
            @tap="changePage('prev')"
          >
            上一页
          </button>
          <text class="page-info">{{ currentPage }}/{{ totalPages }}</text>
          <button
            class="page-btn"
            :disabled="currentPage >= totalPages"
            @tap="changePage('next')"
          >
            下一页
          </button>
        </view>
      </view>
    </view>

    <!-- 添加/编辑用户弹窗 -->
    <view class="modal-mask" v-if="showUserModal" @tap="closeModal"></view>
    <view class="modal-content" v-if="showUserModal" @tap.stop>
      <view class="modal-header">
        <text class="modal-title">{{ isEdit ? "编辑用户" : "添加用户" }}</text>
        <image
          src="../../static/iconfont/closed.png"
          class="close-icon"
          @tap="closeModal"
        />
      </view>

      <view class="modal-form">
        <view class="form-item">
          <text class="form-label">用户名：</text>
          <input
            type="text"
            v-model="userForm.nickName"
            placeholder="请输入用户名"
            class="form-input"
          />
        </view>

        <view class="form-item">
          <text class="form-label">手机号：</text>
          <input
            type="number"
            v-model="userForm.phone"
            placeholder="请输入手机号"
            class="form-input"
          />
        </view>

        <view class="form-item">
          <text class="form-label">用户类型：</text>
          <picker
            :range="userTypeOptions"
            range-key="name"
            :value="userTypeIndex"
            @change="handleUserTypeChange"
            class="form-picker"
          >
            <view class="picker-value">
              {{ userTypeOptions[userTypeIndex].name }}
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="form-label">余额：</text>
          <input
            type="number"
            v-model="userForm.balance"
            placeholder="请输入余额"
            class="form-input"
          />
        </view>
      </view>

      <view class="modal-footer">
        <button class="cancel-btn" @tap="closeModal">取消</button>
        <button class="confirm-btn" @tap="confirmUserForm">确认</button>
      </view>
    </view>

    <!-- 添加/编辑礼物弹窗 -->
    <view class="modal-mask" v-if="showGiftModal" @tap="closeModal"></view>
    <view class="modal-content" v-if="showGiftModal" @tap.stop>
      <view class="modal-header">
        <text class="modal-title">{{ isEdit ? "编辑礼物" : "添加礼物" }}</text>
        <image
          src="../../static/iconfont/closed.png"
          class="close-icon"
          @tap="closeModal"
        />
      </view>

      <view class="modal-form">
        <view class="form-item">
          <text class="form-label">礼物名称：</text>
          <input
            type="text"
            v-model="giftForm.name"
            placeholder="请输入礼物名称"
            class="form-input"
          />
        </view>

        <view class="form-item">
          <text class="form-label">礼物价格：</text>
          <input
            type="number"
            v-model="giftForm.price"
            placeholder="请输入礼物价格"
            class="form-input"
          />
        </view>

        <view class="form-item">
          <text class="form-label">礼物图片：</text>
          <view class="upload-box">
            <image
              v-if="giftForm.url"
              :src="giftForm.url"
              class="preview-image"
              mode="aspectFit"
            />
            <view class="upload-btn" @tap="chooseImage">
              {{ giftForm.url ? "更换图片" : "上传图片" }}
            </view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="cancel-btn" @tap="closeModal">取消</button>
        <button class="confirm-btn" @tap="confirmGiftForm">确认</button>
      </view>
    </view>

    <!-- 查看订单详情弹窗 -->
    <view
      class="modal-mask"
      v-if="showOrderDetailModal"
      @tap="closeModal"
    ></view>
    <view
      class="modal-content order-detail-modal"
      v-if="showOrderDetailModal"
      @tap.stop
    >
      <view class="modal-header">
        <text class="modal-title">订单详情</text>
        <image
          src="../../static/iconfont/closed.png"
          class="close-icon"
          @tap="closeModal"
        />
      </view>

      <view class="order-detail">
        <view class="detail-item">
          <text class="detail-label">订单号：</text>
          <text class="detail-value">{{ currentOrder.orderNo }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">用户：</text>
          <text class="detail-value">{{ currentOrder.userName }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">金额：</text>
          <text class="detail-value">{{ currentOrder.amount }}虚拟币</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">状态：</text>
          <text class="detail-value">{{
            getOrderStatus(currentOrder.status)
          }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">创建时间：</text>
          <text class="detail-value">{{
            formatDate(currentOrder.createTime)
          }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">微信号：</text>
          <text class="detail-value">{{ currentOrder.wechat || "-" }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">留言：</text>
          <text class="detail-value">{{ currentOrder.message || "-" }}</text>
        </view>
      </view>

      <view class="modal-footer">
        <button class="confirm-btn full-width" @tap="closeModal">关闭</button>
      </view>
    </view>

    <!-- 确认删除弹窗 -->
    <view class="modal-mask" v-if="showDeleteModal" @tap="closeModal"></view>
    <view class="modal-content delete-modal" v-if="showDeleteModal" @tap.stop>
      <view class="delete-title">确认删除</view>
      <view class="delete-message"
        >确定要删除该{{ getDeleteTypeName() }}吗？此操作不可恢复。</view
      >

      <view class="modal-footer">
        <button class="cancel-btn" @tap="closeModal">取消</button>
        <button class="delete-confirm-btn" @tap="confirmDelete">
          确认删除
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { getUserList } from "@/api/user";

// 导航菜单项
const navItems = [
  { key: "users", name: "用户管理" },
  { key: "orders", name: "订单管理" },
  { key: "gifts", name: "礼物管理" },
];

// 当前选中的导航
const currentNav = ref("users");

// 切换导航
const switchNav = (key) => {
  currentNav.value = key;
  currentPage.value = 1;
  searchKeyword.value = "";
  fetchData();
};

// 搜索关键词
const searchKeyword = ref("");

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const totalPages = computed(() => Math.ceil(total.value / pageSize.value));

// 加载状态
const loading = ref(false);

// 数据列表
const userList = ref([]);
const orderList = ref([]);
const giftList = ref([]);

// 模态框相关
const showUserModal = ref(false);
const showGiftModal = ref(false);
const showOrderDetailModal = ref(false);
const showDeleteModal = ref(false);
const isEdit = ref(false);
const deleteType = ref("");
const deleteItem = ref(null);

// 当前查看的订单
const currentOrder = ref({});

// 用户表单
const userForm = reactive({
  id: "",
  nickName: "",
  phone: "",
  userType: 0,
  balance: 0,
});

// 礼物表单
const giftForm = reactive({
  id: "",
  name: "",
  price: 0,
  url: "",
});

// 用户类型选项
const userTypeOptions = [
  { value: 0, name: "普通用户" },
  { value: 1, name: "店员" },
  { value: 2, name: "管理员" },
  { value: 3, name: "申请成为店员" },
];

// 用户类型选择器索引
const userTypeIndex = ref(0);

// 处理用户类型变更
const handleUserTypeChange = (e) => {
  userTypeIndex.value = e.detail.value;
  userForm.userType = userTypeOptions[userTypeIndex.value].value;
};

// 获取用户类型名称
const getUserType = (type) => {
  const option = userTypeOptions.find((item) => item.value === type);
  return option ? option.name : "未知";
};

// 获取订单状态名称
const getOrderStatus = (status) => {
  const statusMap = {
    0: "待付款",
    1: "已付款",
    2: "已完成",
    3: "已取消",
  };
  return statusMap[status] || "未知";
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "-";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 获取删除类型名称
const getDeleteTypeName = () => {
  switch (deleteType.value) {
    case "user":
      return "用户";
    case "gift":
      return "礼物";
    default:
      return "项目";
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  fetchData();
};

// 分页切换
const changePage = (direction) => {
  if (direction === "prev" && currentPage.value > 1) {
    currentPage.value--;
  } else if (direction === "next" && currentPage.value < totalPages.value) {
    currentPage.value++;
  }
  fetchData();
};

// 显示添加模态框
const showAddModal = (type) => {
  isEdit.value = false;
  if (type === "user") {
    resetUserForm();
    showUserModal.value = true;
  } else if (type === "gift") {
    resetGiftForm();
    showGiftModal.value = true;
  }
};

// 处理编辑
const handleEdit = (type, item) => {
  isEdit.value = true;
  if (type === "user") {
    Object.assign(userForm, item);
    const typeIndex = userTypeOptions.findIndex(
      (option) => option.value === item.userType
    );
    userTypeIndex.value = typeIndex >= 0 ? typeIndex : 0;
    showUserModal.value = true;
  } else if (type === "gift") {
    Object.assign(giftForm, item);
    showGiftModal.value = true;
  } else if (type === "order") {
    // 编辑订单逻辑
  }
};

// 处理查看
const handleView = (type, item) => {
  if (type === "order") {
    currentOrder.value = item;
    showOrderDetailModal.value = true;
  }
};

// 处理删除
const handleDelete = (type, item) => {
  deleteType.value = type;
  deleteItem.value = item;
  showDeleteModal.value = true;
};

// 确认删除
const confirmDelete = () => {
  // TODO: 调用删除API
  console.log(`删除${deleteType.value}:`, deleteItem.value);

  // 模拟删除成功
  uni.showToast({
    title: "删除成功",
    icon: "success",
  });

  closeModal();
  fetchData();
};

// 重置用户表单
const resetUserForm = () => {
  userForm.id = "";
  userForm.nickName = "";
  userForm.phone = "";
  userForm.userType = 1;
  userForm.balance = 0;
  userTypeIndex.value = 0;
};

// 重置礼物表单
const resetGiftForm = () => {
  giftForm.id = "";
  giftForm.name = "";
  giftForm.price = 0;
  giftForm.url = "";
};

// 确认用户表单
const confirmUserForm = () => {
  // 表单验证
  if (!userForm.nickName) {
    uni.showToast({
      title: "请输入用户名",
      icon: "none",
    });
    return;
  }

  // TODO: 调用保存API
  console.log("保存用户:", userForm);

  // 模拟保存成功
  uni.showToast({
    title: isEdit.value ? "编辑成功" : "添加成功",
    icon: "success",
  });

  closeModal();
  fetchData();
};

// 确认礼物表单
const confirmGiftForm = () => {
  // 表单验证
  if (!giftForm.name) {
    uni.showToast({
      title: "请输入礼物名称",
      icon: "none",
    });
    return;
  }

  if (!giftForm.price || giftForm.price <= 0) {
    uni.showToast({
      title: "请输入有效的礼物价格",
      icon: "none",
    });
    return;
  }

  if (!giftForm.url) {
    uni.showToast({
      title: "请上传礼物图片",
      icon: "none",
    });
    return;
  }

  // TODO: 调用保存API
  console.log("保存礼物:", giftForm);

  // 模拟保存成功
  uni.showToast({
    title: isEdit.value ? "编辑成功" : "添加成功",
    icon: "success",
  });

  closeModal();
  fetchData();
};

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      // 这里应该上传图片到服务器，获取URL
      // 这里仅做演示，使用本地临时路径
      giftForm.url = res.tempFilePaths[0];
    },
  });
};

// 关闭模态框
const closeModal = () => {
  showUserModal.value = false;
  showGiftModal.value = false;
  showOrderDetailModal.value = false;
  showDeleteModal.value = false;
};

// 获取数据
const fetchData = () => {
  loading.value = true;

  if (currentNav.value === "users") {
    getUserList({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
    })
      .then((res) => {
        if (res && res.data) {
          userList.value = res.data.list.map((user) => {
            // 处理null值
            return {
              id: user.userId,
              nickName: user.nickName || "空",
              avatar: user.avatar || "../../static/default-avatar.png",
              age: user.age || "空",
              sex: user.sex === null ? "空" : user.sex === 1 ? "男" : "女",
              phone: user.phone || "空",
              province: user.province || "空",
              isOnline:
                user.isOnline === null ? "空" : user.isOnline ? "在线" : "离线",
              level: user.level || "空",
              balance: user.balance || 0,
              totalAmount: user.totalAmount || 0,
              signature: user.signature || "空",
              tags: user.tags || "空",
              userType: user.userType,
              voice: user.voice || "空",
              voiceTime: user.voiceTime || "空",
            };
          });
          total.value = res.data.total;
        }
        loading.value = false;
      })
      .catch((err) => {
        console.error("获取用户列表失败", err);
        loading.value = false;
        uni.showToast({
          title: "获取数据失败",
          icon: "none",
        });
      });
  } else if (currentNav.value === "orders") {
    // ... 其他代码 ...
    loading.value = false;
  } else if (currentNav.value === "gifts") {
    // 模拟礼物数据
    giftList.value = [
      { id: 1, name: "鲜花", price: 10, url: "../../static/iconfont/logo.png" },
      { id: 2, name: "蛋糕", price: 20, url: "../../static/iconfont/logo.png" },
      { id: 3, name: "钻石", price: 50, url: "../../static/iconfont/logo.png" },
    ];
    total.value = giftList.value.length;
    loading.value = false;
  }
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.admin-container {
  min-height: 100vh;
  background-color: #f5f5f5;

  .admin-header {
    background-color: #409eff;
    padding: 30rpx;
    text-align: center;

    .admin-title {
      color: #fff;
      font-size: 36rpx;
      font-weight: bold;
    }
  }

  .admin-nav {
    display: flex;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;

    .nav-item {
      flex: 1;
      text-align: center;
      padding: 30rpx 0;
      font-size: 28rpx;
      color: #333;
      position: relative;

      &.active {
        color: #409eff;
        font-weight: bold;

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 25%;
          width: 50%;
          height: 4rpx;
          background-color: #409eff;
        }
      }
    }
  }

  .admin-content {
    padding: 20rpx;

    .content-section {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30rpx;

        .section-title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }

        .add-btn {
          background-color: #409eff;
          color: #fff;
          font-size: 24rpx;
          padding: 10rpx 30rpx;
          border-radius: 30rpx;
        }
      }

      .search-bar {
        display: flex;
        margin-bottom: 30rpx;

        .search-input {
          flex: 1;
          height: 70rpx;
          border: 1rpx solid #ddd;
          border-radius: 35rpx;
          padding: 0 30rpx;
          font-size: 26rpx;
        }

        .search-btn {
          width: 150rpx;
          height: 70rpx;
          line-height: 70rpx;
          background-color: #409eff;
          color: #fff;
          font-size: 26rpx;
          border-radius: 35rpx;
          margin-left: 20rpx;
        }
      }

      .data-table {
        border: 1rpx solid #eee;
        border-radius: 8rpx;
        overflow: hidden;

        .table-header {
          display: flex;
          background-color: #f8f8f8;

          .th {
            flex: 1;
            padding: 20rpx;
            font-size: 26rpx;
            font-weight: bold;
            text-align: center;
            color: #333;

            &:last-child {
              flex: 1.5;
            }
          }
        }

        .table-row {
          display: flex;
          border-top: 1rpx solid #eee;

          &:hover {
            background-color: #f5f5f5;
          }

          .td {
            flex: 1;
            padding: 20rpx;
            font-size: 26rpx;
            text-align: center;
            color: #666;

            &:last-child {
              flex: 1.5;
            }

            &.actions {
              display: flex;
              justify-content: center;
              gap: 10rpx;

              .action-btn {
                font-size: 22rpx;
                padding: 6rpx 16rpx;
                border-radius: 20rpx;

                &.view {
                  background-color: #67c23a;
                  color: #fff;
                }

                &.edit {
                  background-color: #409eff;
                  color: #fff;
                }

                &.delete {
                  background-color: #f56c6c;
                  color: #fff;
                }
              }
            }
          }
        }

        .empty-tip {
          padding: 50rpx 0;
          text-align: center;
          color: #999;
          font-size: 26rpx;
        }
      }
    }

    .gift-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;
      margin-bottom: 30rpx;

      .gift-item {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        align-items: center;

        .gift-image {
          width: 120rpx;
          height: 120rpx;
          margin-bottom: 20rpx;
        }

        .gift-info {
          text-align: center;
          margin-bottom: 20rpx;

          .gift-name {
            display: block;
            font-size: 28rpx;
            color: #333;
            margin-bottom: 10rpx;
          }

          .gift-price {
            display: block;
            font-size: 26rpx;
            color: #f56c6c;
          }
        }

        .gift-actions {
          display: flex;
          gap: 20rpx;

          .action-btn {
            font-size: 22rpx;
            padding: 6rpx 16rpx;
            border-radius: 20rpx;

            &.edit {
              background-color: #409eff;
              color: #fff;
            }

            &.delete {
              background-color: #f56c6c;
              color: #fff;
            }
          }
        }
      }

      .gift-empty {
        grid-column: span 3;
      }
    }

    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 30rpx;

      .page-btn {
        width: 150rpx;
        height: 60rpx;
        line-height: 60rpx;
        text-align: center;
        background-color: #409eff;
        color: #fff;
        font-size: 26rpx;
        border-radius: 30rpx;

        &:disabled {
          background-color: #ccc;
        }
      }

      .page-info {
        margin: 0 30rpx;
        font-size: 26rpx;
        color: #666;
      }
    }
  }

  .modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }

  .modal-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    z-index: 1000;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;

      .modal-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      .close-icon {
        width: 40rpx;
        height: 40rpx;
      }
    }

    .modal-form {
      .form-item {
        margin-bottom: 30rpx;

        .form-label {
          display: block;
          font-size: 28rpx;
          color: #333;
          margin-bottom: 15rpx;
        }

        .form-input {
          width: 100%;
          height: 80rpx;
          border: 1rpx solid #ddd;
          border-radius: 8rpx;
          padding: 0 20rpx;
          font-size: 26rpx;
          box-sizing: border-box;
        }

        .form-picker {
          width: 100%;
          height: 80rpx;
          border: 1rpx solid #ddd;
          border-radius: 8rpx;
          padding: 0 20rpx;
          font-size: 26rpx;
          box-sizing: border-box;

          .picker-value {
            height: 80rpx;
            line-height: 80rpx;
          }
        }

        .upload-box {
          display: flex;
          flex-direction: column;
          align-items: center;

          .preview-image {
            width: 200rpx;
            height: 200rpx;
            margin-bottom: 20rpx;
            border-radius: 8rpx;
          }

          .upload-btn {
            width: 200rpx;
            height: 60rpx;
            line-height: 60rpx;
            text-align: center;
            background-color: #409eff;
            color: #fff;
            font-size: 26rpx;
            border-radius: 30rpx;
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 20rpx;
      margin-top: 30rpx;

      button {
        width: 180rpx;
        height: 70rpx;
        line-height: 70rpx;
        text-align: center;
        font-size: 28rpx;
        border-radius: 35rpx;

        &.cancel-btn {
          background-color: #f5f5f5;
          color: #666;
        }

        &.confirm-btn {
          background-color: #409eff;
          color: #fff;
        }
      }
    }

    &.order-detail-modal {
      .order-detail {
        .detail-item {
          display: flex;
          margin-bottom: 20rpx;

          .detail-label {
            width: 180rpx;
            font-size: 28rpx;
            color: #666;
          }

          .detail-value {
            flex: 1;
            font-size: 28rpx;
            color: #333;
          }
        }

        .detail-message {
          margin-top: 30rpx;

          .message-label {
            font-size: 28rpx;
            color: #666;
            margin-bottom: 15rpx;
          }

          .message-content {
            padding: 20rpx;
            background-color: #f5f5f5;
            border-radius: 8rpx;
            font-size: 28rpx;
            color: #333;
            min-height: 100rpx;
          }
        }
      }
    }

    &.delete-confirm-modal {
      width: 500rpx;

      .delete-message {
        text-align: center;
        font-size: 30rpx;
        color: #333;
        margin: 30rpx 0 50rpx;
      }
    }
  }
}

.data-table {
  .avatar-cell {
    display: flex;
    justify-content: center;
    align-items: center;

    .user-avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
    }
  }
}
</style>
