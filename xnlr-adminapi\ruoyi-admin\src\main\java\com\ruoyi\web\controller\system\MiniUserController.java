package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.MiniUser;
import com.ruoyi.system.service.IMiniUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小程序用户管理Controller
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@RestController
@RequestMapping("/system/miniapp-users")
public class MiniUserController extends BaseController {
    @Autowired
    private IMiniUserService miniUserService;

    /**
     * 查询小程序用户列表（普通用户和申请成为店员的用户）
     */
    @PreAuthorize("@ss.hasPermi('system:miniapp-users:list')")
    @GetMapping("/list")
    public TableDataInfo list(MiniUser miniUser) {
        // 查询普通用户 (user_type = 0) 和申请成为店员的用户 (user_type = 3)
        // 不设置 userType，在 Mapper 中处理多值查询
        startPage();
        List<MiniUser> list = miniUserService.selectMiniUserListForCustomers(miniUser);
        return getDataTable(list);
    }

    /**
     * 获取小程序用户统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:miniapp-users:list')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        Map<String, Object> stats = miniUserService.getMiniUserStats();
        return success(stats);
    }

    /**
     * 导出小程序用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:miniapp-users:export')")
    @Log(title = "小程序用户管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MiniUser miniUser) {
        // 只导出普通用户 (user_type = 0)
        miniUser.setUserType(0);
        List<MiniUser> list = miniUserService.selectMiniUserList(miniUser);
        ExcelUtil<MiniUser> util = new ExcelUtil<MiniUser>(MiniUser.class);
        util.exportExcel(response, list, "小程序用户数据");
    }

    /**
     * 获取小程序用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:miniapp-users:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") String userId) {
        MiniUser user = miniUserService.selectMiniUserByUserId(userId);
        if (user != null && user.getUserType() != 0) {
            return error("该用户不是普通小程序用户");
        }
        return success(user);
    }

    /**
     * 新增小程序用户
     */
    @PreAuthorize("@ss.hasPermi('system:miniapp-users:add')")
    @Log(title = "小程序用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiniUser miniUser) {
        // 确保用户类型为普通用户
        miniUser.setUserType(0);
        return toAjax(miniUserService.insertMiniUser(miniUser));
    }

    /**
     * 修改小程序用户
     */
    @PreAuthorize("@ss.hasPermi('system:miniapp-users:edit')")
    @Log(title = "小程序用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiniUser miniUser) {
        // 确保用户类型为普通用户
        miniUser.setUserType(0);
        return toAjax(miniUserService.updateMiniUser(miniUser));
    }

    /**
     * 删除小程序用户
     */
    @PreAuthorize("@ss.hasPermi('system:miniapp-users:remove')")
    @Log(title = "小程序用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable String[] userIds) {
        return toAjax(miniUserService.deleteMiniUserByUserIds(userIds));
    }
}
