package com.ruoyi.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;

/**
 * 首页数据统计Mapper接口
 * 
 * <AUTHOR>
 */
public interface DashboardMapper {
    /**
     * 统计总用户数
     * 
     * @return 总用户数
     */
    Long countTotalUsers();

    /**
     * 统计总订单数
     * 
     * @return 总订单数
     */
    Long countTotalOrders();

    /**
     * 统计总收入
     * 
     * @return 总收入
     */
    Long countTotalRevenue();

    /**
     * 统计待审核申请数
     * 
     * @return 待审核申请数
     */
    Long countPendingApplies();

    /**
     * 统计在线用户数
     * 
     * @return 在线用户数
     */
    Long countOnlineUsers();

    /**
     * 统计服务提供者数量
     * 
     * @return 服务提供者数量
     */
    Long countServiceProviders();

    /**
     * 获取用户增长趋势数据
     *
     * @param timeRange 时间范围
     * @return 趋势数据
     */
    List<Map<String, Object>> getUserTrendData(@Param("timeRange") String timeRange);

    /**
     * 获取订单趋势数据
     *
     * @param timeRange 时间范围
     * @return 趋势数据
     */
    List<Map<String, Object>> getOrderTrendData(@Param("timeRange") String timeRange);

    /**
     * 获取服务类型分布数据
     * 
     * @return 分布数据
     */
    List<Map<String, Object>> getServiceDistributionData();

    /**
     * 获取最新订单列表（最近10条）
     * 
     * @return 订单列表
     */
    List<Map<String, Object>> getRecentOrders();

    /**
     * 获取待审核申请列表（最近10条）
     *
     * @return 申请列表
     */
    List<Map<String, Object>> getPendingApplies();

    /**
     * 统计店员数量
     *
     * @return 店员数量
     */
    Long countStaffUsers();
}
