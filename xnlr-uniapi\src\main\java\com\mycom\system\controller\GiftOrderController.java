package com.mycom.system.controller;

import com.mycom.system.domain.GiftOrderEntity;
import com.mycom.system.domain.OrderVo;
import com.mycom.system.service.impl.GiftOrderService;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.mycom.system.common.vo.Result;

import java.util.List;

import javax.annotation.Resource;

/**
 * (GiftOrder)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-25 18:51:27
 */
@RestController
@RequestMapping("giftOrder")
public class GiftOrderController {
    /**
     * 服务对象
     */
    @Resource
    private GiftOrderService giftOrderService;

    /**
     * 通过条件筛选，分页查询
     *
     * @param giftOrderEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list/page")
    public Result<PageInfo<OrderVo>> getListPage(@RequestBody GiftOrderEntity giftOrderEntity) {
        return Result.success(giftOrderService.getListPage(giftOrderEntity));
    }

    /**
     * 通过条件筛选,全部
     *
     * @param giftOrderEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list")
    public Result<List<OrderVo>> getList(@RequestBody GiftOrderEntity giftOrderEntity) {
        return Result.success(giftOrderService.getList(giftOrderEntity));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键 实体
     * @return 单条数据
     */
    @GetMapping("{id}")
    public Result<GiftOrderEntity> queryById(@PathVariable("id") Long id) {
        return Result.success(giftOrderService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param giftOrderEntity 实体
     * @return 新增结果
     */
    @PostMapping("create")
    public Result<GiftOrderEntity> add(@RequestBody GiftOrderEntity giftOrderEntity) {
        return Result.success(giftOrderService.insert(giftOrderEntity));
    }

    /**
     * 编辑数据
     *
     * @param giftOrderEntity 实体
     * @return 编辑结果
     */
    @PutMapping
    public Result<GiftOrderEntity> edit(GiftOrderEntity giftOrderEntity) {
        return Result.success(giftOrderService.update(giftOrderEntity));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public Result<Boolean> deleteById(Long id) {
        return Result.success(giftOrderService.deleteById(id));
    }

}

