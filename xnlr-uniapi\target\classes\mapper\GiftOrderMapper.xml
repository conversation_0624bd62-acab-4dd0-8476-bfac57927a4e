<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.GiftOrderMapper">
    <resultMap id="BaseResultMap" type="com.mycom.system.domain.GiftOrderEntity">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="buyerId" column="buyer_id" jdbcType="INTEGER"/>
        <result property="sellerId" column="seller_id" jdbcType="INTEGER"/>
        <result property="isReward" column="is_reward" jdbcType="INTEGER"/>
        <result property="quantity" column="quantity" jdbcType="INTEGER"/>
        <result property="totalPrice" column="total_price" jdbcType="INTEGER"/>
        <result property="wechat" column="wechat" jdbcType="VARCHAR"/>
        <result property="message" column="message" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        buyer_id,
        seller_id,
        is_reward,
        quantity,
        total_price,
        wechat,
        message,
        order_number,
        payment_time,
        status
    </sql>

    <!--条件查询-->
    <select id="getList" resultType="com.mycom.system.domain.OrderVo">
        select
        gift_order.id,
        buyer_id,
        seller_id,
        is_reward,
        quantity,
        total_price,
        gift_order.wechat,
        message,
        order_number,
        payment_time,
        status,
        gift_order.service_id,
        user_service.service_time as serviceTime,
        service_type.service_name as serviceTypeName,
        user.nick_name as sellerName

        from gift_order
        left join user_service on gift_order.service_id=user_service.id
        left join service_type on user_service.service_type_id = service_type.id
        left join user on gift_order.seller_id = user.user_id
        <where>
            <if test="id != null">
                and gift_order.id = #{id}
            </if>
            <if test="buyerId != null">
                and buyer_id = #{buyerId}
            </if>
            <if test="sellerId != null">
                and seller_id = #{sellerId}
            </if>
            <if test="isReward != null">
                and is_reward = #{isReward}
            </if>
            <if test="quantity != null">
                and quantity = #{quantity}
            </if>
            <if test="totalPrice != null">
                and total_price = #{totalPrice}
            </if>
            <if test="wechat != null and wechat != ''">
                and gift_order.wechat = #{wechat}
            </if>
            <if test="message != null and message != ''">
                and message = #{message}
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and order_number = #{orderNumber}
            </if>
            <if test="paymentTime != null">
                and payment_time = #{paymentTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            and is_reward = 1
        </where>
    </select>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gift_order
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select id,
               buyer_id,
               seller_id,
               is_reward,
               service_id,
               quantity,
               total_price,
               wechat,
               message


        from xnlr.gift_order
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from gift_order
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="buyerId != null">
                and buyer_id = #{buyerId}
            </if>
            <if test="sellerId != null">
                and seller_id = #{sellerId}
            </if>
            <if test="isReward != null">
                and is_reward = #{isReward}
            </if>
            <if test="quantity != null">
                and quantity = #{quantity}
            </if>
            <if test="totalPrice != null">
                and total_price = #{totalPrice}
            </if>
            <if test="wechat != null and wechat != ''">
                and wechat = #{wechat}
            </if>
            <if test="message != null and message != ''">
                and message = #{message}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into gift_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isReward != null">
                is_reward,
            </if>
            <if test="serviceId != null">
                service_id,
            </if>
            <if test="message != null">
                message,
            </if>
            buyer_id,seller_id,quantity,total_price,wechat,payment_time,status,order_number
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isReward != null">
                #{isReward},
            </if>
            <if test="serviceId != null">
                #{serviceId},
            </if>
            <if test="message != null and message != ''">
                #{message},
            </if>
            #{buyerId},#{sellerId},#{quantity},#{totalPrice},#{wechat},#{paymentTime},#{status},#{orderNumber}
        </trim>
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into gift_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="buyerId != null">
                buyer_id,
            </if>
            <if test="sellerId != null">
                seller_id,
            </if>
            <if test="isReward != null">
                is_reward,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
            <if test="totalPrice != null">
                total_price,
            </if>
            <if test="wechat != null and wechat != ''">
                wechat,
            </if>
            <if test="message != null and message != ''">
                message,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="buyerId != null">
                #{buyerId},
            </if>
            <if test="sellerId != null">
                #{sellerId},
            </if>
            <if test="isReward != null">
                #{isReward},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
            <if test="totalPrice != null">
                #{totalPrice},
            </if>
            <if test="wechat != null and wechat != ''">
                #{wechat},
            </if>
            <if test="message != null and message != ''">
                #{message},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into gift_order(buyer_id, seller_id, is_reward, quantity, total_price, wechat, message)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.buyerId}, #{entity.sellerId}, #{entity.isReward}, #{entity.quantity}, #{entity.totalPrice},
            #{entity.wechat}, #{entity.message})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into gift_order(buyer_id, seller_id, is_reward, quantity, total_price, wechat, message)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.buyerId}, #{entity.sellerId}, #{entity.isReward}, #{entity.quantity}, #{entity.totalPrice},
            #{entity.wechat}, #{entity.message})
        </foreach>
        on duplicate key update
        buyer_id = values(buyer_id),
        seller_id = values(seller_id),
        is_reward = values(is_reward),
        quantity = values(quantity),
        total_price = values(total_price),
        wechat = values(wechat),
        message = values(message)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update gift_order
        <set>
            <if test="buyerId != null">
                buyer_id = #{buyerId},
            </if>
            <if test="sellerId != null">
                seller_id = #{sellerId},
            </if>
            <if test="isReward != null">
                is_reward = #{isReward},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="totalPrice != null">
                total_price = #{totalPrice},
            </if>
            <if test="wechat != null and wechat != ''">
                wechat = #{wechat},
            </if>
            <if test="message != null and message != ''">
                message = #{message},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from gift_order where id = #{id}
    </delete>
</mapper>

