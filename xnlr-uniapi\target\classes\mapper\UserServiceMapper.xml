<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.UserServiceMapper">
    <resultMap id="BaseResultMap" type="com.mycom.system.domain.UserServiceEntity">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="serviceTypeId" column="service_type_id" jdbcType="VARCHAR"/>
        <result property="serviceTime" column="service_time" jdbcType="VARCHAR"/>
        <result property="servicePrice" column="service_price" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_List">
        id,
        user_id,
        service_type_id,
        service_time,
        service_price
    </sql>

    <!--条件查询-->
    <select id="getList" resultType="com.mycom.system.domain.UserServiceEntity">
        select user_service.id,
        user_id,
        user_service.service_type_id,
        service_time,
        service_price,
        service_type.service_name,
        service_type.service_description
        from user_service
        left join service_type on user_service.service_type_id = service_type.id
        <where>
            <if test="id != null">
                and user_service.id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="serviceTypeId != null and serviceTypeId != ''">
                and service_type_id = #{serviceTypeId}
            </if>
        </where>
    </select>


    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_List"/>
        from user_service
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select id,
               user_id,
               service_type_id,
               service_time,
               service_price
        from user_service
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from user_service
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="serviceTypeId != null and serviceTypeId != ''">
                and service_type_id = #{serviceTypeId}
            </if>
            <if test="serviceTime != null and serviceTime != ''">
                and service_time = #{serviceTime}
            </if>
            <if test="servicePrice != null">
                and service_price = #{servicePrice}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into user_service(user_id, service_type_id, service_time, service_price)
        values (#{userId}, #{serviceTypeId}, #{serviceTime}, #{servicePrice})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into user_service
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="serviceTypeId != null and serviceTypeId != ''">
                service_type_id,
            </if>
            <if test="serviceTime != null and serviceTime != ''">
                service_time,
            </if>
            <if test="servicePrice != null">
                service_price,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="serviceTypeId != null and serviceTypeId != ''">
                #{serviceTypeId},
            </if>
            <if test="serviceTime != null and serviceTime != ''">
                #{serviceTime},
            </if>
            <if test="servicePrice != null">
                #{servicePrice},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into user_service(user_id, service_type_id, service_time, service_price)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.serviceTypeId}, #{entity.serviceTime}, #{entity.servicePrice})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into user_service(user_id, service_type_id, service_time, service_price)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.serviceTypeId}, #{entity.serviceTime}, #{entity.servicePrice})
        </foreach>
        on duplicate key update
        user_id = values(user_id),
        service_type_id = values(service_type_id),
        service_time = values(service_time),
        service_price = values(service_price)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update user_service
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="serviceTypeId != null and serviceTypeId != ''">
                service_type_id = #{serviceTypeId},
            </if>
            <if test="serviceTime != null and serviceTime != ''">
                service_time = #{serviceTime},
            </if>
            <if test="servicePrice != null">
                service_price = #{servicePrice},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from user_service where id = #{id}
    </delete>
</mapper>

