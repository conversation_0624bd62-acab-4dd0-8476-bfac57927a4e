package com.mycom.system.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户统计数据VO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class UserStatsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户类型分布
     */
    private List<StatItem> userTypeStats;

    /**
     * 性别分布
     */
    private List<StatItem> genderStats;

    /**
     * 在线状态分布
     */
    private List<StatItem> onlineStats;

    /**
     * 地域分布(TOP 10)
     */
    private List<StatItem> provinceStats;
}
