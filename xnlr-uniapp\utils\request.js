/*
 * @Author: zmy
 * @Date: 2025-02-20 16:09:05
 * @LastEditTime: 
 * @Description:
 */
import { getStorageSync } from './utils'
import { BASE_DEVURL } from '@/config/index'
import RequestManager from '@/utils/requestManager.js'

const manager = new RequestManager()
const baseRequest = async (url, method, data = {}, type = 'json', loading = true) => {
  let requestId = manager.generateId(method, url, data)
  if (!requestId) {
    console.log('重复请求')
  }
  if (!requestId) return false

  const header = {}
  header.Authorization = 'Bearer ' + getStorageSync('token') || ''
  if (type === 'form') {
    header['content-type'] = 'application/x-www-form-urlencoded'
  }else if (type === 'multipart') {
    header['content-type'] = 'multipart/form-data';
  }
  return new Promise((reslove, reject) => {
    // loading && uni.showLoading({ title: '加急请求中...' })
    uni.request({
	  url: BASE_DEVURL + url,
      method: method || 'get',
      header: header,
      timeout: 30000,
      data: data || {},
      complete: () => {
        setTimeout(() => {
          uni.hideLoading()
        }, 500)
        manager.deleteById(requestId)
      },
      success: successData => {
        const res = successData.data
        if (res.code == 200) {
          reslove(res.data || res)
        } else if (res.code === 401) {
          console.log('接口401 要重新登录')
          uni.setStorageSync('hasLogin', false)
          uni.removeStorageSync('token')
          uni.removeStorageSync('userInfo')
        } else {
          const { msg } = res
          uni.showToast({
            title: msg || '网络连接失败，请稍后重试',
            icon: 'none',
            duration: 2000
          })
          reject(res)
        }
      },
      fail: msg => {
        uni.showToast({
          title: msg || '网络连接失败，请稍后重试',
          icon: 'none',
          duration: 2000
        })
        reject(msg)
      }
    })
  })
}

const request = {}

;['options', 'get', 'post', 'put', 'head', 'delete', 'trace', 'connect'].forEach(method => {
  request[method] = (api, data, type, loading) => baseRequest(api, method, data, type, loading)
})

export default request
