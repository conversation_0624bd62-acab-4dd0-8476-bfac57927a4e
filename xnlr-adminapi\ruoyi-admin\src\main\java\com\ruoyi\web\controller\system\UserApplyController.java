package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.UserApply;
import com.ruoyi.system.service.IUserApplyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 申请管理Controller
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
@RestController
@RequestMapping("/system/UserApply")
public class UserApplyController extends BaseController {
    @Autowired
    private IUserApplyService userApplyService;

    /**
     * 查询申请管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:UserApply:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserApply userApply) {
        startPage();
        List<UserApply> list = userApplyService.selectUserApplyList(userApply);
        return getDataTable(list);
    }

    /**
     * 导出申请管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:UserApply:export')")
    @Log(title = "申请管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserApply userApply) {
        List<UserApply> list = userApplyService.selectUserApplyList(userApply);
        ExcelUtil<UserApply> util = new ExcelUtil<UserApply>(UserApply.class);
        util.exportExcel(response, list, "申请管理数据");
    }

    /**
     * 获取申请管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:UserApply:query')")
    @GetMapping(value = "/{userApplyId}")
    public AjaxResult getInfo(@PathVariable("userApplyId") Long userApplyId) {
        return success(userApplyService.selectUserApplyByUserApplyId(userApplyId));
    }

    /**
     * 新增申请管理
     */
    @PreAuthorize("@ss.hasPermi('system:UserApply:add')")
    @Log(title = "申请管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserApply userApply) {
        return toAjax(userApplyService.insertUserApply(userApply));
    }

    /**
     * 修改申请管理
     */
    @PreAuthorize("@ss.hasPermi('system:UserApply:edit')")
    @Log(title = "申请管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserApply userApply) {
        return toAjax(userApplyService.updateUserApply(userApply));
    }

    /**
     * 删除申请管理
     */
    @PreAuthorize("@ss.hasPermi('system:UserApply:remove')")
    @Log(title = "申请管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userApplyIds}")
    public AjaxResult remove(@PathVariable Long[] userApplyIds) {
        return toAjax(userApplyService.deleteUserApplyByUserApplyIds(userApplyIds));
    }

    /**
     * 同意申请
     */
    @PreAuthorize("@ss.hasPermi('system:UserApply:edit')")
    @Log(title = "申请管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{userApplyId}/approve")
    public AjaxResult approve(@PathVariable Long userApplyId) {
        return toAjax(userApplyService.approveUserApply(userApplyId));
    }

    /**
     * 拒绝申请
     */
    @PreAuthorize("@ss.hasPermi('system:UserApply:edit')")
    @Log(title = "申请管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{userApplyId}/reject")
    public AjaxResult reject(@PathVariable Long userApplyId) {
        return toAjax(userApplyService.rejectUserApply(userApplyId));
    }
}
