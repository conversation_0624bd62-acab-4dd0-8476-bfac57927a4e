<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.LeaveMessageMapper">
    <resultMap id="BaseResultMap" type="com.mycom.system.domain.LeaveMessageEntity">
        <!--@Table leave_message-->
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        user_id,
        content
    </sql>

    <!--查询单个-->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from leave_message
        where id = #{id}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select id,
               user_id,
               content


        from xnlr.leave_message
    </select>
    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from leave_message
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into leave_message(user_id, content)
        values (#{userId}, #{content})
    </insert>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into leave_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="content != null and content != ''">
                content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="content != null and content != ''">
                #{content},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into leave_message(user_id, content)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.content})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into leave_message(user_id, content)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}, #{entity.content})
        </foreach>
        on duplicate key update
        user_id = values(user_id),
        content = values(content)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update leave_message
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from leave_message where id = #{id}
    </delete>

    <!--条件查询-->
    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from leave_message
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
        </where>
    </select>
</mapper>

