package com.mycom.system.service.impl;

import com.mycom.system.domain.LeaveMessageEntity;
import com.mycom.system.mapper.LeaveMessageMapper;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;
import java.util.List;

/**
 * (LeaveMessage)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-23 10:17:12
 */
@Service("leaveMessageService")
public class LeaveMessageService {
    @Resource
    private LeaveMessageMapper leaveMessageMapper;

    /**
     * 通过条件筛选，分页查询
     *
     * @param leaveMessageEntity 查询条件
     * @return 多条数据
     */
    public PageInfo<LeaveMessageEntity> getListPage(LeaveMessageEntity leaveMessageEntity) {
        PageHelper.startPage(leaveMessageEntity.getPageNum(), leaveMessageEntity.
                getPageSize());
        List<LeaveMessageEntity> list = leaveMessageMapper.getList(leaveMessageEntity);
        return new PageInfo<>(list);
    }

    /**
     * 通过条件筛选
     *
     * @param leaveMessageEntity 查询条件
     * @return 多条数据
     */
    public List<LeaveMessageEntity> getList(LeaveMessageEntity leaveMessageEntity) {
        List<LeaveMessageEntity> list = leaveMessageMapper.getList(leaveMessageEntity);
        return list;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    public LeaveMessageEntity queryById(Long id) {
        return leaveMessageMapper.selectByPrimaryKey(id);
    }

    /**
     * 新增数据
     *
     * @param leaveMessageEntity 实例对象
     * @return 实例对象
     */
    public LeaveMessageEntity insert(LeaveMessageEntity leaveMessageEntity) {
        leaveMessageMapper.insert(leaveMessageEntity);
        return leaveMessageEntity;
    }

    /**
     * 修改数据
     *
     * @param leaveMessageEntity 实例对象
     * @return 实例对象
     */
    public LeaveMessageEntity update(LeaveMessageEntity leaveMessageEntity) {
        leaveMessageMapper.update(leaveMessageEntity);
        return queryById(leaveMessageEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public boolean deleteById(Long id) {
        return leaveMessageMapper.deleteById(id) > 0;
    }


}

