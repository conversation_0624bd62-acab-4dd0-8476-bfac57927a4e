<template>
	<view class="mine-container">
		<!-- 头部信息 -->
		<view class="mine-header">
			<!-- 左侧头像区域 -->
			<view class="avatar-section">
				<image :src="userInfo.avatar || defaultAvatar" class="avatar" mode="aspectFill"></image>
			</view>

			<!-- 右侧信息区域 -->
			<view class="info-section">
				<!-- 昵称编辑区 -->
				<view class="nickname-box">
					<text class="nickname">{{ userInfo.nickName || '未设置昵称' }}</text>
					<view class="edit-nickname" @tap="toMineDetail">
						<image src="../..//static/iconfont/right.png" class="edit-icon"></image>
					</view>
				</view>

				<!-- VIP等级进度条 -->
				<view class="vip-progress">
					<view class="vip-info">
						<text class="vip-level">VIP{{ vipLevel }}</text>
						<text class="vip-amount">充值金额：{{ userInfo.totalAmount || 0 }}元</text>
					</view>
					<view class="progress-bar">
						<view class="progress-inner"
							:style="{ width: progressWidth + '%', backgroundColor: progressColor }"></view>
					</view>
					<view class="progress-tips">
						距离下一等级还需充值{{ nextLevelNeed }}元
					</view>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="mine-menu">
			<view class="menu-item">
				<image src="../../static/iconfont/order.svg" class="menu-icon"></image>
				<text class="menu-text">我的订单</text>
				<view class="menu-right" @tap="handleOrder">
					<text class="menu-value">全部订单</text>
					<image src="@/static/iconfont/right.png" class="arrow-icon"></image>
				</view>
			</view>
			<view class="menu-item">
				<image src="../../static/iconfont/wallet.svg" class="menu-icon"></image>
				<text class="menu-text">我的钱包</text>
				<view class="menu-right" @tap="handleWallet">
					<text class="menu-value">{{ userInfo.balance }}虚拟币</text>
					<image src="@/static/iconfont/right.png" class="arrow-icon"></image>
				</view>
			</view>
			<view class="menu-item">
				<image src="../../static/iconfont/staff.svg" class="menu-icon"></image>
				<text class="menu-text">成为店员</text>
				<view class="menu-right" @tap="handleBecomeStaff">
					<text class="menu-value"></text>
					<image src="@/static/iconfont/right.png" class="arrow-icon"></image>
				</view>
			</view>
		</view>

		<!-- 昵称修改弹窗 -->
		<view class="edit-popup" v-if="showNicknameEdit">
			<view class="popup-content">
				<view class="popup-title">修改昵称</view>
				<input type="text" v-model="newNickname" class="nickname-input" placeholder="请输入新昵称" maxlength="12" />
				<view class="popup-buttons">
					<button class="cancel-btn" @tap="showNicknameEdit = false">取消</button>
					<button class="confirm-btn" @tap="confirmEditNickname">确定</button>
				</view>
			</view>
		</view>
		<custom-tab-bar></custom-tab-bar>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import CustomTabBar from '@/components/custom-tab-bar/index.vue'
import { updateUserInfo } from '@/api/user'
import { getUserInfo } from '@/api/user'
import { chooseAvatar } from '@/api/file'
import { onShow } from '@dcloudio/uni-app'

// 默认头像
const defaultAvatar = '../../static/iconfont/logo.png'

// 用户信息
const userInfo = ref({
	userId: '',
	avatar: '',
	nickName: '',
	totalAmount: 0,
	balance: 0,
	userType: 0
})

// VIP等级计算
const vipLevel = computed(() => {
	const amount = userInfo.value.totalAmount || 0
	if (amount >= 10000) return 5
	if (amount >= 5000) return 4
	if (amount >= 2000) return 3
	if (amount >= 500) return 2
	if (amount >= 100) return 1
	return 0
})

// 进度条宽度计算
const progressWidth = computed(() => {
	const amount = userInfo.value.totalAmount || 0
	const levelThresholds = [0, 100, 500, 2000, 5000, 10000]
	const currentLevel = vipLevel.value
	const currentThreshold = levelThresholds[currentLevel]
	const nextThreshold = levelThresholds[currentLevel + 1]

	if (currentLevel >= 5) return 100
	return ((amount - currentThreshold) / (nextThreshold - currentThreshold)) * 100
})

// 进度条颜色
const progressColor = computed(() => {
	const colors = [
		'#b4b4b4',
		'#67c23a',
		'#409eff',
		'#e6a23c',
		'#f56c6c',
		'#9c27b0'
	]
	return colors[vipLevel.value]
})

// 下一等级所需金额
const nextLevelNeed = computed(() => {
	const amount = userInfo.value.totalAmount || 0
	const levelThresholds = [100, 500, 2000, 5000, 10000]
	const currentLevel = vipLevel.value

	if (currentLevel >= 5) return 0
	return levelThresholds[currentLevel] - amount
})

// 昵称修改相关
const showNicknameEdit = ref(false)
const newNickname = ref('')
// 确认修改昵称
const confirmEditNickname = async () => {
	if (!newNickname.value.trim()) {
		uni.showToast({
			title: '昵称不能为空',
			icon: 'none'
		})
		return
	}
	try {
		const userEntity = {
			nickName: newNickname.value,
			userId: userInfo.value.userId
		}
		await updateUserInfo(userEntity)
		await getUserInfoApp()
		uni.showToast({
			title: '修改成功',
			icon: 'success'
		})
		showNicknameEdit.value = false
	} catch (error) {
		uni.showToast({
			title: '修改失败',
			icon: 'none'
		})
	}
}

// 处理订单
const handleOrder = () => {
	uni.navigateTo({
		url: '../order/order?userId=' + userInfo.value.userId
	})
}
const handleWallet = () => {
	uni.navigateTo({
		url: '../e-wallet/e-wallet'
	})
}


// 处理成为店员
const handleBecomeStaff = () => {
	uni.navigateTo({
		url: '../staff-apply/staff-apply'
	})
}

// 跳转个人中心
const toMineDetail = () => {
	uni.navigateTo({
		url: '../mine-detail/mine-detail'
	})
}

// 获取用户信息
const getUserInfoApp = async () => {
	let userId = 0;
	const res = await getUserInfo(userId)
	userInfo.value = res
}

onShow(() => {
	getUserInfoApp()
})
</script>

<style lang="scss" scoped>
.mine-container {
	min-height: 100vh;
	padding: 0 15rpx;
	background-color: #f5f5f5;

	.mine-header {
		background-color: #ffffff;
		padding: 40rpx 30rpx;
		border-radius: 20rpx;
		display: flex;

		.avatar-section {
			position: relative;
			margin-right: 50rpx;

			.avatar {
				width: 180rpx;
				height: 180rpx;
				border-radius: 90rpx;
			}

			.edit-avatar {
				color: #fff;
				font-size: 22rpx;
				background-color: #ff70b7;
				position: absolute;
				right: -5rpx;
				bottom: -3rpx;
				border-radius: 20rpx;
				padding: 6rpx 18rpx;
			}
		}

		.info-section {
			flex: 1;
			margin-top: 10rpx;

			.nickname-box {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;
				justify-content: space-between;

				.nickname {
					font-size: 32rpx;
					font-weight: 600;
					margin-right: 20rpx;
				}

				.edit-nickname {
					padding: 6rpx;

					.edit-icon {
						width: 24rpx;
						height: 24rpx;
						font-weight: 600;
					}
				}
			}

			.vip-progress {
				.vip-info {
					display: flex;
					justify-content: space-between;
					margin-bottom: 10rpx;

					.vip-level {
						font-size: 24rpx;
						color: #666;
					}

					.vip-amount {
						font-size: 24rpx;
						color: #999;
					}
				}

				.progress-bar {
					height: 10rpx;
					background-color: #eee;
					border-radius: 5rpx;
					overflow: hidden;

					.progress-inner {
						height: 100%;
						transition: width 0.3s ease;
					}
				}

				.progress-tips {
					margin-top: 15rpx;
					font-size: 22rpx;
					color: #999;
					text-align: right;
				}
			}
		}
	}

	.mine-menu {
		margin-top: 20rpx;
		background-color: #ffffff;
		border-radius: 20rpx;

		.menu-item {
			display: flex;
			align-items: center;
			padding: 50rpx 30rpx 50rpx 50rpx;
			border-bottom: 1rpx solid #d9d9d9;

			.menu-icon {
				width: 40rpx;
				height: 40rpx;
				margin-right: 20rpx;
			}

			.menu-text {
				flex: 1;
				font-size: 28rpx;
				color: #333;
			}

			.menu-right {
				display: flex;
				align-items: center;

				.menu-value {
					font-size: 26rpx;
					color: #999;
					margin-right: 10rpx;
				}

				.arrow-icon {
					width: 30rpx;
					height: 30rpx;
				}
			}
		}
	}
}

// 昵称修改弹窗
.edit-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 999;

	.popup-content {
		width: 600rpx;
		background: #fff;
		border-radius: 20rpx;
		padding: 40rpx;

		.popup-title {
			font-size: 32rpx;
			font-weight: 600;
			text-align: center;
			margin-bottom: 30rpx;
		}

		.nickname-input {
			width: 100%;
			height: 80rpx;
			border: 1rpx solid #eee;
			border-radius: 10rpx;
			padding: 0 20rpx;
			font-size: 28rpx;
			margin-bottom: 30rpx;
		}

		.popup-buttons {
			display: flex;
			justify-content: space-between;

			button {
				width: 45%;
				height: 80rpx;
				line-height: 80rpx;
				font-size: 28rpx;
				border-radius: 40rpx;

				&.cancel-btn {
					background: #f5f5f5;
					color: #666;
				}

				&.confirm-btn {
					background: #409eff;
					color: #fff;
				}
			}
		}
	}
}
</style>
