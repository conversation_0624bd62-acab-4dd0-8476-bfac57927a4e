<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mycom.system.mapper.GiftReceiveMapper">
    <sql id="Base_List">
        select id,
               user_id,
               gift_id,
               quantity
        from gift_receive
    </sql>
    <select id="getList" resultType="com.mycom.system.domain.GiftReceiveEntity">
        select gift_receive.id,
        gift_receive.user_id,
        gift_receive.gift_id,
        gift_receive.quantity,
        gift.name,
        gift.price,
        gift.url
        from gift_receive
        left join gift on gift_receive.gift_id = gift.id
        where gift_receive.user_id = #{userId}
    </select>

    <select id="selectById" resultType="com.mycom.system.domain.GiftReceiveEntity">
        <include refid="Base_List">
        </include>
        where id = #{id}
    </select>

    <update id="update">
        update gift_receive
        <set>
            <if test="quantity != null">
                quantity = #{quantity}
            </if>
        </set>
        where user_id=#{userId} and gift_id=#{giftId}
    </update>

    <select id="selectByUserIdAndGiftId" resultType="com.mycom.system.domain.GiftReceiveEntity">
        <include refid="Base_List">
        </include>
        where user_id=#{userId} and gift_id=#{giftId}
    </select>

    <insert id="insert" parameterType="com.mycom.system.domain.GiftReceiveEntity">
        insert into gift_receive(user_id,gift_id,quantity)
        values(#{userId},#{giftId},#{quantity})
    </insert>
</mapper>

