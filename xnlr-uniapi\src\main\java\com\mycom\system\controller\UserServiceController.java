package com.mycom.system.controller;

import com.mycom.system.domain.UserServiceEntity;
import com.mycom.system.service.impl.UserServiceService;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.mycom.system.common.vo.Result;

import javax.annotation.Resource;
import java.util.List;

/**
 * (Service)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-23 14:38:54
 */
@RestController
@RequestMapping("user/service")
public class UserServiceController {
    /**
     * 服务对象
     */
    @Resource
    private UserServiceService userServiceService;

    /**
     * 通过条件筛选，分页查询
     *
     * @param userServiceEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list/page")
    public Result<PageInfo<UserServiceEntity>> getListPage(@RequestBody UserServiceEntity userServiceEntity) {
        return Result.success(userServiceService.getListPage(userServiceEntity));
    }

    /**
     * 通过条件筛选，分页查询
     *
     * @param userServiceEntity 查询条件
     * @return 多条数据
     */
    @PostMapping("list")
    public Result<List<UserServiceEntity>> getList(@RequestBody UserServiceEntity userServiceEntity) {
        return Result.success(userServiceService.getList(userServiceEntity));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键 实体
     * @return 单条数据
     */
    @GetMapping("{id}")
    public Result<UserServiceEntity> queryById(@PathVariable("id") Long id) {
        return Result.success(userServiceService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param userServiceEntity 实体
     * @return 新增结果
     */
    @PostMapping
    public Result<UserServiceEntity> add(UserServiceEntity userServiceEntity) {
        return Result.success(userServiceService.insert(userServiceEntity));
    }

    /**
     * 编辑数据
     *
     * @param userServiceEntity 实体
     * @return 编辑结果
     */
    @PutMapping
    public Result<UserServiceEntity> edit(UserServiceEntity userServiceEntity) {
        return Result.success(userServiceService.update(userServiceEntity));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public Result<Boolean> deleteById(Long id) {
        return Result.success(userServiceService.deleteById(id));
    }

}

