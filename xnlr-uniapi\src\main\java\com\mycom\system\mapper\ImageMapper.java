package com.mycom.system.mapper;

import com.mycom.system.domain.ImageEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-26 15:41:04
 */
public interface ImageMapper {

    /**
     * 条件查询
     *
     * @param imageEntity
     */
    List<ImageEntity> getList(ImageEntity imageEntity);

    /**
     * 通过ID查询
     *
     * @param id
     */
    ImageEntity selectByPrimaryKey(Long id);

    /**
     * 新增数据
     *
     * @param imageEntity
     */
    int insert(ImageEntity imageEntity);


    /**
     * 修改数据
     *
     * @param imageEntity 实例对象
     */
    int update(ImageEntity imageEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     */
    int deleteById(Long id);

}

