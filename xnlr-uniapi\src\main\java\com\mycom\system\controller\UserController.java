package com.mycom.system.controller;

import com.mycom.system.domain.UserEntity;
import com.mycom.system.domain.UserForm;
import com.mycom.system.service.impl.UserService;
import com.mycom.system.common.vo.Result;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 用户表(User)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-20 20:30:57
 */
@RestController
@RequestMapping("user")
public class UserController {
    /**
     * 服务对象
     */
    @Resource
    private UserService userService;

    /**
     * 普通用户查询店员列表
     *
     * @param userForm
     * @return
     */
    @PostMapping("list/page")
    public Result<PageInfo<UserEntity>> getList(@RequestBody UserForm userForm) {
        return Result.success(userService.getList(userForm));
    }

    /**
     * 上传头像
     *
     * @return
     */
    @PostMapping("/avatar/upload")
    public Result<String> uploadAvatar(MultipartFile file) {
        String url = userService.uploadAvatar(file);
        return Result.success(url);
    }


    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("info/{userId}")
    public Result<UserEntity> queryById(@PathVariable Long userId) {
        return Result.success(userService.queryById(userId));
    }

    /**
     * 新增数据
     *
     * @param userEntity 实体
     * @return 新增结果
     */
    @PostMapping
    public Result<UserEntity> add(UserEntity userEntity) {
        return Result.success(userService.insert(userEntity));
    }

    /**
     * 编辑数据
     *
     * @param userEntity 实体
     * @return 编辑结果
     */
    @PutMapping("update")
    public Result<UserEntity> edit(@RequestBody UserEntity userEntity) {
        return Result.success(userService.update(userEntity));
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return 删除是否成功
     */
    @DeleteMapping
    public Result<Boolean> deleteById(Long id) {
        return Result.success(userService.deleteById(id));
    }

}

