<template>
    <view class="tab-bar">
        <view v-for="(item, index) in tabs" :key="index" class="tab-item" :class="{ active: getCurrent() === index }"
            @tap="switchTab(item.pagePath, index)">
            <image :src="getCurrent() === index ? item.selectedIconPath : item.iconPath"></image>
            <text>{{ item.text }}</text>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const current = ref(0)

const tabs = [
    {
        "pagePath": "pages/home/<USER>",
        "text": "首页",
        "iconPath": "../../static/iconfont/house.svg",
        "selectedIconPath": "../../static/iconfont/house-fill.svg"
    },
    {
        "pagePath": "pages/blindbox/blindbox",
        "text": "盲盒下单",
        "iconPath": "../../static/iconfont/box.svg",
        "selectedIconPath": "../../static/iconfont/box-fill.svg"
    },
    {
        "pagePath": "pages/mine/mine",
        "text": "我的",
        "iconPath": "../../static/iconfont/person.svg",
        "selectedIconPath": "../../static/iconfont/person-fill.svg"
    }
]

const getCurrent = () => {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentPath = currentPage.route

    const index = tabs.findIndex(item => item.pagePath === currentPath)
    return index !== -1 ? index : 0
}

const switchTab = (path, index) => {
    current.value = index
    uni.switchTab({
        url: `/${path}`
    })
}

uni.$on('tabPageShow', () => {
    current.value = getCurrent()
})

onMounted(() => {
    current.value = getCurrent()
})
</script>

<style lang="scss">
.tab-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 130rpx;
    background: white;
    display: flex;
    padding-bottom: env(safe-area-inset-bottom);

    .tab-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        image {
            width: 50rpx;
            height: 50rpx;
        }

        text {
            font-size: 24rpx;
            color: #42aaff;
            margin-top: 4rpx;
        }

        &.active {
            text {
                color: #ff87c3;
            }
        }
    }
}
</style>