package com.mycom.system.controller;

import com.mycom.system.domain.UserApplyEntity;
import com.mycom.system.service.impl.UserApplyService;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.mycom.system.common.vo.Result;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-03-08 18:08:40
 */
@RestController
@RequestMapping("userApply")
public class UserApplyController {

    @Resource
    private UserApplyService userApplyService;

    /**
     * 条件筛选，分页查询
     *
     * @param userApplyEntity
     */
    @PostMapping("list/page")
    public Result<PageInfo<UserApplyEntity>> getListPage(@RequestBody UserApplyEntity userApplyEntity) {
        return Result.success(userApplyService.getListPage(userApplyEntity));
    }

    /**
     * 条件筛选,查询全部
     *
     * @param userApplyEntity
     */
    @PostMapping("list")
    public Result<List<UserApplyEntity>> getList(@RequestBody UserApplyEntity userApplyEntity) {
        return Result.success(userApplyService.getList(userApplyEntity));
    }


    /**
     * 主键查询单条数据
     *
     * @param id
     */
    @GetMapping("{id}")
    public Result<UserApplyEntity> queryById(@PathVariable("id") Long id) {
        return Result.success(userApplyService.queryById(id));
    }

    /**
     * 新增店员
     *
     * @param userApplyEntity
     */
    @PostMapping("/insert")
    public Result<String> add(@ModelAttribute UserApplyEntity userApplyEntity) {
        userApplyService.insert(userApplyEntity);
        return Result.success();
    }

    /**
     * 新增普通
     *
     * @param userApplyEntity
     */
    @PostMapping("/insert/common")
    public Result<String> addCommon(@RequestBody UserApplyEntity userApplyEntity) {
        userApplyService.insertCommon(userApplyEntity);
        return Result.success();
    }

    /**
     * 编辑数据
     *
     * @param userApplyEntity
     */
    @PutMapping("/update")
    public Result<UserApplyEntity> edit(UserApplyEntity userApplyEntity) {
        return Result.success(userApplyService.update(userApplyEntity));
    }

    /**
     * 删除数据
     *
     * @param id
     */
    @DeleteMapping("/delete")
    public Result<Boolean> deleteById(Long id) {
        return Result.success(userApplyService.deleteById(id));
    }

}

