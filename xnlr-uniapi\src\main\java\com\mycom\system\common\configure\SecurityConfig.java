package com.mycom.system.common.configure;

import cn.hutool.core.collection.CollectionUtil;
import com.mycom.system.common.security.CorsFilter;
import com.mycom.system.common.security.DefaultAuthenticationEntryPoint;
import com.mycom.system.common.security.DefaultAuthenticationTokenFilter;
import com.mycom.system.common.security.DefaultLogoutSuccessHandler;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter;

import javax.annotation.Resource;
import java.util.List;

/**
 * spring security配置
 */
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    /**
     * security 允许访问配置
     */
    @Resource
    SecurityAntMatchersConfig securityAntMatchersConfig;
    /**
     * 自定义用户认证逻辑
     */
    @Qualifier("userDetailsServiceImpl")
    @Resource
    private UserDetailsService userDetailsService;
    /**
     * 认证失败处理类
     */
    @Resource
    private DefaultAuthenticationEntryPoint defaultAuthenticationEntryPoint;
    /**
     * 退出处理类
     */
    @Resource
    private DefaultLogoutSuccessHandler defaultLogoutSuccessHandler;
    /**
     * token认证过滤器
     */
    @Resource
    private DefaultAuthenticationTokenFilter defaultAuthenticationTokenFilter;
    /**
     * （跨源资源共享）过滤器，处理跨域请求。
     */
    @Resource
    private CorsFilter myCorsFilter;


    /**
     * 解决 无法直接注入 AuthenticationManager
     *
     * @return
     * @throws Exception
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    /**
     * anyRequest          |   匹配所有请求路径
     * access              |   SpringEl表达式结果为true时可以访问
     * anonymous           |   匿名可以访问
     * denyAll             |   用户不能访问
     * fullyAuthenticated  |   用户完全认证可以访问（非remember-me下自动登录）
     * hasAnyAuthority     |   如果有参数，参数表示权限，则其中任何一个权限可以访问
     * hasAnyRole          |   如果有参数，参数表示角色，则其中任何一个角色可以访问
     * hasAuthority        |   如果有参数，参数表示权限，则其权限可以访问
     * hasIpAddress        |   如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问
     * hasRole             |   如果有参数，参数表示角色，则其角色可以访问
     * permitAll           |   用户可以任意访问
     * rememberMe          |   允许通过remember-me登录的用户访问
     * authenticated       |   用户登录后可访问
     */
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry expressionInterceptUrlRegistry = httpSecurity
                // CRSF禁用，因为不使用session
                .csrf().disable()
                // 认证失败处理类
                .exceptionHandling().authenticationEntryPoint(defaultAuthenticationEntryPoint).and()
                // 基于token，所以不需要session
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                // 过滤请求
                .authorizeRequests()
                //新填
                .antMatchers(HttpMethod.OPTIONS, "**").permitAll();
        // 对于登录login 验证码captchaImage 允许匿名访问
        expressionInterceptUrlRegistry.antMatchers("/login", "/getTokenInfo", "/sso/login", "/getToken", "/getTitle").anonymous()
                .antMatchers(
                        HttpMethod.GET,
                        "/*.html",
                        "/**/*.html",
                        "/**/*.css",
                        "/**/*.js"
                ).permitAll()
                .antMatchers("/profile/**").anonymous()
                .antMatchers("/common/download**").anonymous()
                .antMatchers("/common/download/resource**").anonymous();


        // 配置文件不拦截
        List<String> permitAlls = securityAntMatchersConfig.getPermitAll();
        if (CollectionUtil.isNotEmpty(permitAlls)) {
            for (int i = 0; i < permitAlls.size(); i++) {
                String s = permitAlls.get(i);
                expressionInterceptUrlRegistry.antMatchers(s).permitAll();
            }
        }
        // 配置文件运行匿名访问
        List<String> anonymouss = securityAntMatchersConfig.getAnonymous();
        if (CollectionUtil.isNotEmpty(anonymouss)) {
            for (int i = 0; i < anonymouss.size(); i++) {
                String s = anonymouss.get(i);
                expressionInterceptUrlRegistry.antMatchers(s).anonymous();
            }
        }
        // 除上面外的所有请求全部需要鉴权认证
        expressionInterceptUrlRegistry.anyRequest().authenticated()
                .and()
                .headers().frameOptions().disable();
        // httpSecurity.logout().logoutUrl("/logout").logoutSuccessHandler(defaultLogoutSuccessHandler);
        // 添加JWT filter
        httpSecurity.addFilterBefore(defaultAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
        httpSecurity.addFilterBefore(myCorsFilter, WebAsyncManagerIntegrationFilter.class);
    }


    /**
     * 强散列哈希加密实现
     */
    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

}
