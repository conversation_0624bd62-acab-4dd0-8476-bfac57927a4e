package com.mycom.system.controller;

import com.mycom.system.domain.ImageEntity;
import com.mycom.system.service.impl.ImageService;
import org.springframework.web.bind.annotation.*;
import com.github.pagehelper.PageInfo;
import com.mycom.system.common.vo.Result;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-02-26 15:41:04
 */
@RestController
@RequestMapping("image")
public class ImageController {

    @Resource
    private ImageService imageService;

    /**
     * 条件筛选，分页查询
     *
     * @param imageEntity
     */
    @PostMapping("list/page")
    public Result<PageInfo<ImageEntity>> getListPage(@RequestBody ImageEntity imageEntity) {
        return Result.success(imageService.getListPage(imageEntity));
    }

    /**
     * 条件筛选,查询全部
     *
     * @param imageEntity
     */
    @PostMapping("list")
    public Result<List<ImageEntity>> getList(@RequestBody ImageEntity imageEntity) {
        return Result.success(imageService.getList(imageEntity));
    }


    /**
     * 主键查询单条数据
     *
     * @param id
     */
    @GetMapping("{id}")
    public Result<ImageEntity> queryById(@PathVariable("id") Long id) {
        return Result.success(imageService.queryById(id));
    }

    /**
     * 新增数据
     *
     * @param imageEntity
     */
    @PostMapping("/insert")
    public Result<ImageEntity> add(ImageEntity imageEntity) {
        return Result.success(imageService.insert(imageEntity));
    }

    /**
     * 编辑数据
     *
     * @param imageEntity
     */
    @PutMapping("/update")
    public Result<ImageEntity> edit(ImageEntity imageEntity) {
        return Result.success(imageService.update(imageEntity));
    }

    /**
     * 删除数据
     *
     * @param id
     */
    @DeleteMapping("/delete")
    public Result<Boolean> deleteById(Long id) {
        return Result.success(imageService.deleteById(id));
    }

}

