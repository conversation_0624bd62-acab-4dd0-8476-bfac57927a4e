package com.mycom.system.service.impl;

import com.mycom.system.domain.ImageEntity;
import com.mycom.system.mapper.ImageMapper;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-26 15:41:04
 */
@Service("imageService")
public class ImageService {
    @Resource
    private ImageMapper imageMapper;

    /**
     * 条件筛选，分页查询
     *
     * @param imageEntity
     */
    public PageInfo<ImageEntity> getListPage(ImageEntity imageEntity) {
        PageHelper.startPage(imageEntity.getPageNum(), imageEntity.
                getPageSize());
        List<ImageEntity> list = imageMapper.getList(imageEntity);
        return new PageInfo<>(list);
    }

    /**
     * 条件筛选，查询全部
     *
     * @param imageEntity
     */
    public List<ImageEntity> getList(ImageEntity imageEntity) {
        List<ImageEntity> list = imageMapper.getList(imageEntity);
        return list;
    }

    /**
     * 通过ID查询
     *
     * @param id
     */
    public ImageEntity queryById(Long id) {
        return imageMapper.selectByPrimaryKey(id);
    }

    /**
     * 插入
     *
     * @param imageEntity
     */
    public ImageEntity insert(ImageEntity imageEntity) {
        imageMapper.insert(imageEntity);
        return imageEntity;
    }

    /**
     * 修改
     *
     * @param imageEntity
     */
    public ImageEntity update(ImageEntity imageEntity) {
        imageMapper.update(imageEntity);
        return queryById(imageEntity.getId());
    }

    /**
     * 删除
     *
     * @param id 主键
     */
    public boolean deleteById(Long id) {
        return imageMapper.deleteById(id) > 0;
    }
}

