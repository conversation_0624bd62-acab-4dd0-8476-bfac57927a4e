package com.mycom.system.mapper;

import com.mycom.system.domain.UserApplyEntity;
import com.mycom.system.domain.vo.StatItem;
import com.mycom.system.domain.vo.RecentApplyVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-08 18:08:40
 */
public interface UserApplyMapper {

    /**
     * 条件查询
     *
     * @param userApplyEntity
     */
    List<UserApplyEntity> getList(UserApplyEntity userApplyEntity);

    /**
     * 通过ID查询
     *
     * @param userApplyId
     */
    UserApplyEntity selectByPrimaryKey(Long userApplyId);

    /**
     * 新增数据
     *
     * @param userApplyEntity
     */
    int insert(UserApplyEntity userApplyEntity);

    /**
     * 修改数据
     *
     * @param userApplyEntity 实例对象
     */
    int update(UserApplyEntity userApplyEntity);

    /**
     * 通过主键删除数据
     *
     * @param userApplyId 主键
     */
    int deleteById(Long userApplyId);

    void insertCommon(UserApplyEntity userApplyEntity);

    // ========== 数据统计相关方法 ==========

    /**
     * 统计总申请数
     */
    Long countTotalApplies();

    /**
     * 统计待审核申请数
     */
    Long countPendingApplies();

    /**
     * 统计申请状态分布
     */
    List<StatItem> getApplyStatusStats();

    /**
     * 获取最近申请(5条)
     */
    List<RecentApplyVO> getRecentApplies();

}
