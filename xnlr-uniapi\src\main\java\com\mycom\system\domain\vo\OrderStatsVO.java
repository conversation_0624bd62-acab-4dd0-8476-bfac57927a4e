package com.mycom.system.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单统计数据VO
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class OrderStatsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单状态分布
     */
    private List<StatItem> statusStats;

    /**
     * 总收入
     */
    private Long totalRevenue;

    /**
     * 平均订单金额
     */
    private Double avgOrderAmount;

    /**
     * 最近订单(5条)
     */
    private List<RecentOrderVO> recentOrders;
}
