package com.mycom.system.domain;

import java.io.Serializable;

import lombok.Data;


/**
 * (ServiceType)实体类
 *
 * <AUTHOR>
 * @since 2025-02-26 10:01:02
 */


@Data

public class ServiceTypeEntity implements Serializable {
    private static final long serialVersionUID = -64071435935763290L;
    /**
     * 服务类型id
     */
    private Long id;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 服务描述
     */
    private String serviceDescription;


    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

}

