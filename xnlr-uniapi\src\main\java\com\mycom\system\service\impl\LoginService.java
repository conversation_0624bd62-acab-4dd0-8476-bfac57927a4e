package com.mycom.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.mycom.system.domain.LoginFormWx;
import com.mycom.system.domain.LoginUser;
import com.mycom.system.domain.UserEntity;
import com.mycom.system.common.rt.WeChatApiRestTemplate;
import com.mycom.system.common.utils.StringUtils;
import com.mycom.system.common.exception.ServiceException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

@Service("loginService")
public class LoginService {

    @Resource
    private WeChatApiRestTemplate weChatApiRestTemplate;

    @Resource
    private UserService userService;

    @Resource
    private TokenService tokenService;

    public Map<String, Object> loginWeiXin(LoginFormWx loginFormWx) {
        if (StringUtils.isBlank(loginFormWx.getCode())) {
            throw new ServiceException(String.format("微信登录必须参数不能为空"));
        }
        // 根据code查询openid
        JSONObject SessionKeyOpenId = weChatApiRestTemplate.loginWeChat(loginFormWx);
        String openid = SessionKeyOpenId.getString("openid");
        String sessionKey = SessionKeyOpenId.getString("session_key");
        if (StringUtils.isBlank(openid) || StringUtils.isBlank(sessionKey)) {
            throw new ServiceException("网络错误，请重试");
        }
        UserEntity userEntity = userService.getUserByOpenId(openid);
        // 用户不存在则插入
        if (userEntity == null) {
            userEntity = new UserEntity();
            userEntity.setOpenid(openid);
            userEntity.setUserType(1);
            userEntity.setCreatedAt(new Date());
            userEntity.setAvatar(loginFormWx.getAvatar());
            userEntity.setNickName(loginFormWx.getNickName());
            userService.insert(userEntity);
        }
        // 用户存在则更新
//        else if (StringUtils.equals(openid, userEntity.getOpenid())) {
//            userService.update(userEntity);
//        }
        LoginUser loginUser = new LoginUser();
        loginUser.setUser(userEntity);
        return tokenService.createToken(loginUser);

    }
}
