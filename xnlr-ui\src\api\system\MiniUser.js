import request from '@/utils/request'

// 查询用户管理列表
export function listMiniUser(query) {
  return request({
    url: '/system/MiniUser/list',
    method: 'get',
    params: query
  })
}

// 查询用户管理详细
export function getMiniUser(userId) {
  return request({
    url: '/system/MiniUser/' + userId,
    method: 'get'
  })
}

// 新增用户管理
export function addMiniUser(data) {
  return request({
    url: '/system/MiniUser',
    method: 'post',
    data: data
  })
}

// 修改用户管理
export function updateMiniUser(data) {
  return request({
    url: '/system/MiniUser',
    method: 'put',
    data: data
  })
}

// 删除用户管理
export function delMiniUser(userId) {
  return request({
    url: '/system/MiniUser/' + userId,
    method: 'delete'
  })
}
