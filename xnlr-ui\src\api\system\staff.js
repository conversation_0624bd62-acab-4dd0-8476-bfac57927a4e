import request from '@/utils/request'

// 查询店员列表 (实际调用MiniUser接口，但只查询店员)
export function listStaff(query) {
  return request({
    url: '/system/staff/list',
    method: 'get',
    params: query
  })
}

// 查询店员详细
export function getStaff(userId) {
  return request({
    url: '/system/staff/' + userId,
    method: 'get'
  })
}

// 新增店员
export function addStaff(data) {
  return request({
    url: '/system/staff',
    method: 'post',
    data: data
  })
}

// 修改店员
export function updateStaff(data) {
  return request({
    url: '/system/staff',
    method: 'put',
    data: data
  })
}

// 删除店员
export function delStaff(userId) {
  return request({
    url: '/system/staff/' + userId,
    method: 'delete'
  })
}

// 启用/禁用店员
export function changeStaffStatus(userId, status) {
  return request({
    url: '/system/staff/' + userId + '/status',
    method: 'put',
    data: { enabled: status }
  })
}
