package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户管理对象 user
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
public class MiniUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键，用户唯一标识 */
    private Long userId;

    /** openid */
    @Excel(name = "openid")
    private String openid;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 微信 */
    @Excel(name = "微信")
    private String wechat;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 余额 */
    @Excel(name = "余额")
    private Integer balance;

    /** 角色 */
    @Excel(name = "角色")
    private Integer userType;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private String age;

    /** 省份 */
    @Excel(name = "省份")
    private String province;

    /** 标签  */
    @Excel(name = "标签 ")
    private String tags;

    /** 是否在线 */
    @Excel(name = "是否在线")
    private Integer isOnline;

    /** 起步价格 */
    @Excel(name = "起步价格")
    private Long basePrice;

    /** 签名 */
    @Excel(name = "签名")
    private String signature;

    /** 等级 */
    @Excel(name = "等级")
    private String level;

    /** 充值总额 */
    @Excel(name = "充值总额")
    private Integer totalAmount;

    /** 语音 */
    @Excel(name = "语音")
    private String voice;

    /** 语音时长 */
    @Excel(name = "语音时长")
    private Long voiceTime;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Integer enabled;

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }
    public void setWechat(String wechat) 
    {
        this.wechat = wechat;
    }

    public String getWechat() 
    {
        return wechat;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setBalance(Integer balance) 
    {
        this.balance = balance;
    }

    public Integer getBalance() 
    {
        return balance;
    }
    public void setUserType(Integer userType)
    {
        this.userType = userType;
    }

    public Integer getUserType()
    {
        return userType;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }
    public void setSex(String sex) 
    {
        this.sex = sex;
    }

    public String getSex() 
    {
        return sex;
    }
    public void setAge(String age) 
    {
        this.age = age;
    }

    public String getAge() 
    {
        return age;
    }
    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }
    public void setTags(String tags) 
    {
        this.tags = tags;
    }

    public String getTags() 
    {
        return tags;
    }
    public void setIsOnline(Integer isOnline) 
    {
        this.isOnline = isOnline;
    }

    public Integer getIsOnline() 
    {
        return isOnline;
    }
    public void setBasePrice(Long basePrice) 
    {
        this.basePrice = basePrice;
    }

    public Long getBasePrice() 
    {
        return basePrice;
    }
    public void setSignature(String signature) 
    {
        this.signature = signature;
    }

    public String getSignature() 
    {
        return signature;
    }
    public void setLevel(String level) 
    {
        this.level = level;
    }

    public String getLevel() 
    {
        return level;
    }
    public void setTotalAmount(Integer totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public Integer getTotalAmount() 
    {
        return totalAmount;
    }
    public void setVoice(String voice) 
    {
        this.voice = voice;
    }

    public String getVoice() 
    {
        return voice;
    }
    public void setVoiceTime(Long voiceTime) 
    {
        this.voiceTime = voiceTime;
    }

    public Long getVoiceTime() 
    {
        return voiceTime;
    }

    public Integer getEnabled() {
        return enabled;
    }
    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("openid", getOpenid())
            .append("nickName", getNickName())
            .append("avatar", getAvatar())
            .append("wechat", getWechat())
            .append("phone", getPhone())
            .append("balance", getBalance())
            .append("userType", getUserType())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .append("sex", getSex())
            .append("age", getAge())
            .append("province", getProvince())
            .append("tags", getTags())
            .append("isOnline", getIsOnline())
            .append("basePrice", getBasePrice())
            .append("signature", getSignature())
            .append("level", getLevel())
            .append("totalAmount", getTotalAmount())
            .append("voice", getVoice())
            .append("voiceTime", getVoiceTime())
            .toString();
    }
}
