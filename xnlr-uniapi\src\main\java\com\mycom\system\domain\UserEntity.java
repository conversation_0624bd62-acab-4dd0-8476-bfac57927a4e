package com.mycom.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

import lombok.Data;


/**
 * 用户表(User)实体类
 *
 * <AUTHOR>
 * @since 2025-02-20 20:31:00
 */


@Data
public class UserEntity implements Serializable {
    private static final long serialVersionUID = 276221149554498992L;
    /**
     * 主键，用户唯一标识
     */
    private Long userId;

    /**
     * 微信用户唯一标识（唯一约束）
     */
    private String openid;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像URL
     */
    private String avatar;
    /**
     * 用户语音URL
     */
    private String voice;
    /**
     * 用户语音时长
     */
    private Integer voiceTime;

    /**
     * 用户微信号（实时更新）
     */
    private String wechat;

    /**
     * 用户手机号（实时更新）
     */
    private String phone;

    /**
     * 用户余额（默认0）
     */
    private Integer balance;
    /**
     * 用户总额（默认0）
     */
    private Integer totalAmount;

    /**
     * 用户角色：0-用户，1-店员，2-管理员
     */
    private Integer userType;

    /**
     * 性别 0-女，1-男
     */
    private Integer sex;
    /**
     * 年龄
     */
    private Integer age;

    /**
     * 省份
     */
    private String province;

    /**
     * 标签
     */
    private String tags;

    /**
     * 是否在线
     */
    private Boolean isOnline;

    /**
     * 基础价格
     */
    private Integer basePrice;

    /**
     * 签名
     */
    private String signature;

    /**
     * 等级
     */
    private String level;


    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

}

