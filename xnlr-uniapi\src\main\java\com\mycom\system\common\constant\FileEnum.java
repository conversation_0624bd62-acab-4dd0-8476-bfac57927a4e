package com.mycom.system.common.constant;


public enum FileEnum {
    AVATAR("avatar", "头像文件"),
    VOICE("voice", "语音文件");

    private final String fileType; // 文件种类
    private final String description; // 文件描述

    // 构造函数
    FileEnum(String fileType, String description) {
        this.fileType = fileType;
        this.description = description;
    }

    // 根据文件种类获取枚举值
    public static FileEnum fromFileType(String fileType) {
        for (FileEnum type : FileEnum.values()) {
            if (type.getFileType().equalsIgnoreCase(fileType)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的文件种类: " + fileType);
    }

    // 获取文件种类
    public String getFileType() {
        return fileType;
    }

    // 获取文件描述
    public String getDescription() {
        return description;
    }
}
