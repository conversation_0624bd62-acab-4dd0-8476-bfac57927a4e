package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.UserApply;

/**
 * 申请管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
public interface IUserApplyService {
    /**
     * 查询申请管理
     * 
     * @param userApplyId 申请管理主键
     * @return 申请管理
     */
    public UserApply selectUserApplyByUserApplyId(Long userApplyId);

    /**
     * 查询申请管理列表
     * 
     * @param userApply 申请管理
     * @return 申请管理集合
     */
    public List<UserApply> selectUserApplyList(UserApply userApply);

    /**
     * 新增申请管理
     * 
     * @param userApply 申请管理
     * @return 结果
     */
    public int insertUserApply(UserApply userApply);

    /**
     * 修改申请管理
     * 
     * @param userApply 申请管理
     * @return 结果
     */
    public int updateUserApply(UserApply userApply);

    /**
     * 批量删除申请管理
     * 
     * @param userApplyIds 需要删除的申请管理主键集合
     * @return 结果
     */
    public int deleteUserApplyByUserApplyIds(Long[] userApplyIds);

    /**
     * 删除申请管理信息
     *
     * @param userApplyId 申请管理主键
     * @return 结果
     */
    public int deleteUserApplyByUserApplyId(Long userApplyId);

    /**
     * 同意申请
     *
     * @param userApplyId 申请管理主键
     * @return 结果
     */
    public int approveUserApply(Long userApplyId);

    /**
     * 拒绝申请
     *
     * @param userApplyId 申请管理主键
     * @return 结果
     */
    public int rejectUserApply(Long userApplyId);
}
