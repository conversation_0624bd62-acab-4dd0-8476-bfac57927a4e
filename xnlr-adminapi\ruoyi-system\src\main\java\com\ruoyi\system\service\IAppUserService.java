package com.ruoyi.system.service;


import com.ruoyi.common.core.domain.entity.AppUser;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface IAppUserService {
    /**
     * 通过用户账号查询用户
     *
     * @param userName 用户账号
     * @return 用户对象信息
     */
    AppUser selectAppUserByUserName(String userName);

    /**
     * 修改用户信息
     *
     * @param appUser 用户信息
     * @return 结果
     */
    int updateAppUser(AppUser appUser);

    /**
     * 密码校验
     *
     * @param password 明文
     * @param salt     盐
     * @param hashPwd  密文
     * @return boolean
     */
    boolean checkPassword(String password, String salt, String hashPwd);
}

