package com.mycom.system.mapper;

import com.mycom.system.domain.GiftEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (gift)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-25 10:29:21
 */
public interface GiftMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    GiftEntity selectByPrimaryKey(Long id);

    /**
     * 统计总行数
     *
     * @param giftEntity 查询条件
     * @return 总行数
     */
    long count(GiftEntity giftEntity);

    /**
     * 新增数据
     *
     * @param giftEntity 实例对象
     * @return 影响行数
     */
    int insert(GiftEntity giftEntity);


    /**
     * 新增数据
     *
     * @param giftEntity 实例对象
     * @return 影响行数
     */
    int insertSelective(GiftEntity giftEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<GiftEntity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<GiftEntity> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<GiftEntity> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<GiftEntity> entities);

    /**
     * 修改数据
     *
     * @param giftEntity 实例对象
     * @return 影响行数
     */
    int update(GiftEntity giftEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 条件查询
     *
     * @param giftEntity 查询条件
     * @return 对象列表
     */
    List<GiftEntity> getList(GiftEntity giftEntity);

}

