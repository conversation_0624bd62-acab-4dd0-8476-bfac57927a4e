package com.ruoyi.web.controller.system;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.IDashboardService;

/**
 * 首页数据统计
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dashboard")
public class DashboardController extends BaseController {
    @Autowired
    private IDashboardService dashboardService;

    /**
     * 获取首页统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:dashboard:stats')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        Map<String, Object> stats = dashboardService.getDashboardStats();
        return success(stats);
    }

    /**
     * 获取用户增长趋势数据
     */
    @PreAuthorize("@ss.hasPermi('system:dashboard:charts')")
    @GetMapping("/userTrend")
    public AjaxResult getUserTrend(@RequestParam(defaultValue = "7d") String timeRange) {
        try {
            List<Map<String, Object>> trendData = dashboardService.getUserTrendData(timeRange);
            return success(trendData);
        } catch (Exception e) {
            logger.error("获取用户增长趋势数据失败，时间范围: {}", timeRange, e);
            return error("获取用户增长趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取订单趋势数据
     */
    @PreAuthorize("@ss.hasPermi('system:dashboard:charts')")
    @GetMapping("/orderTrend")
    public AjaxResult getOrderTrend(@RequestParam(defaultValue = "7d") String timeRange) {
        try {
            List<Map<String, Object>> trendData = dashboardService.getOrderTrendData(timeRange);
            return success(trendData);
        } catch (Exception e) {
            logger.error("获取订单趋势数据失败，时间范围: {}", timeRange, e);
            return error("获取订单趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取服务类型分布数据
     */
    @PreAuthorize("@ss.hasPermi('system:dashboard:charts')")
    @GetMapping("/serviceDistribution")
    public AjaxResult getServiceDistribution() {
        List<Map<String, Object>> distributionData = dashboardService.getServiceDistributionData();
        return success(distributionData);
    }

    /**
     * 获取最新订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:dashboard:orders')")
    @GetMapping("/recentOrders")
    public AjaxResult getRecentOrders() {
        List<Map<String, Object>> recentOrders = dashboardService.getRecentOrders();
        return success(recentOrders);
    }

    /**
     * 获取待审核申请列表
     */
    @PreAuthorize("@ss.hasPermi('system:dashboard:applies')")
    @GetMapping("/pendingApplies")
    public AjaxResult getPendingApplies() {
        List<Map<String, Object>> pendingApplies = dashboardService.getPendingApplies();
        return success(pendingApplies);
    }
}