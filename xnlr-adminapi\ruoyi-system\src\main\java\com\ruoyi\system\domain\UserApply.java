package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 申请管理对象 user_apply
 * 
 * <AUTHOR>
 * @date 2025-03-09
 */
public class UserApply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请表id */
    private Long userApplyId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 用户手机号（实时更新） */
    @Excel(name = "用户手机号", readConverterExp = "实=时更新")
    private String phone;

    /** 性别 0=女 1-男 */
    @Excel(name = "性别 0=女 1-男")
    private String sex;

    /** 年龄 */
    @Excel(name = "年龄")
    private String age;

    /** 语音url */
    @Excel(name = "语音url")
    private String voice;

    /** 所在城市 */
    @Excel(name = "所在城市")
    private String city;

    /** 经验 */
    @Excel(name = "经验")
    private String experience;

    /** 语音时间 */
    @Excel(name = "语音时间")
    private Long voiceTime;

    /** 申请状态 */
    @Excel(name = "申请状态")
    private String status;

    /** 用户类型 */
    @Excel(name = "用户类型")
    private Integer userType;

    /** 用户头像 */
    @Excel(name = "用户头像")
    private String avatar;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 标签
     */
    private String tags;

    /**
     * 签名
     */
    private String signature;

    /**
     * 等级
     */
    private String level;

    /**
     * 申请类型
     */
    private String applyType;

    public void setUserApplyId(Long userApplyId) 
    {
        this.userApplyId = userApplyId;
    }

    public Long getUserApplyId() 
    {
        return userApplyId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setSex(String sex) 
    {
        this.sex = sex;
    }

    public String getSex() 
    {
        return sex;
    }
    public void setAge(String age) 
    {
        this.age = age;
    }

    public String getAge() 
    {
        return age;
    }
    public void setVoice(String voice) 
    {
        this.voice = voice;
    }

    public String getVoice() 
    {
        return voice;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setExperience(String experience) 
    {
        this.experience = experience;
    }

    public String getExperience() 
    {
        return experience;
    }
    public void setVoiceTime(Long voiceTime) 
    {
        this.voiceTime = voiceTime;
    }

    public Long getVoiceTime() 
    {
        return voiceTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setUserType(Integer userType)
    {
        this.userType = userType;
    }

    public Integer getUserType()
    {
        return userType;
    }
    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }

    public String getWechat() {
        return wechat;
    }
    public void setWechat(String wechat) {
        this.wechat = wechat;
    }
    public String getTags() {
        return tags;
    }
    public void setTags(String tags) {
        this.tags = tags;
    }
    public String getSignature() {
        return signature;
    }
    public void setSignature(String signature) {
        this.signature = signature;
    }
    public String getLevel() {
        return level;
    }
    public void setLevel(String level) {
        this.level = level;
    }
    public String getApplyType() {
        return applyType;
    }
    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userApplyId", getUserApplyId())
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("phone", getPhone())
            .append("sex", getSex())
            .append("age", getAge())
            .append("voice", getVoice())
            .append("city", getCity())
            .append("experience", getExperience())
            .append("voiceTime", getVoiceTime())
            .append("status", getStatus())
            .append("userType", getUserType())
            .append("avatar", getAvatar())
            .toString();
    }
}
