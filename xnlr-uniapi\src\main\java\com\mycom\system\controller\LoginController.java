package com.mycom.system.controller;

import com.mycom.system.domain.LoginFormWx;
import com.mycom.system.service.impl.LoginService;
import com.mycom.system.common.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/login")
public class LoginController {

    @Resource
    private LoginService loginService;

    /**
     * 小程序微信授权登录
     */
    @PostMapping("/wechat")
    public Result<?> loginWeiXin(@RequestBody LoginFormWx loginFormWx) {
        return Result.success(loginService.loginWeiXin(loginFormWx));
    }
}
