package com.mycom.system.common.exception;

/**
 * Created by zhoutaoo on 2018/6/2.
 */
public class ServiceException extends BaseException {


    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String msg;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ServiceException() {
    }

    public ServiceException(String message) {
        this.msg = message;
    }

    public ServiceException(String message, Integer code) {
        this.msg = message;
        this.code = code;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public ServiceException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }

    @Override
    public String getMessage() {
        return msg;
    }

    public ServiceException setMessage(String message) {
        this.msg = message;
        return this;
    }

    public Integer getCode() {
        return code;
    }


}
