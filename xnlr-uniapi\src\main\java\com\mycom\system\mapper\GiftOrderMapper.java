package com.mycom.system.mapper;

import com.mycom.system.domain.GiftOrderEntity;
import com.mycom.system.domain.OrderVo;
import com.mycom.system.domain.vo.StatItem;
import com.mycom.system.domain.vo.RecentOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (gift_order)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-25 18:51:27
 */
public interface GiftOrderMapper {

    /**
     * 条件查询
     *
     * @param giftOrderEntity 查询条件
     * @return 对象列表
     */
    List<OrderVo> getList(GiftOrderEntity giftOrderEntity);

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    GiftOrderEntity selectByPrimaryKey(Long id);

    /**
     * 统计总行数
     *
     * @param giftOrderEntity 查询条件
     * @return 总行数
     */
    long count(GiftOrderEntity giftOrderEntity);

    /**
     * 新增数据
     *
     * @param giftOrderEntity 实例对象
     * @return 影响行数
     */
    int insert(GiftOrderEntity giftOrderEntity);

    /**
     * 新增数据
     *
     * @param giftOrderEntity 实例对象
     * @return 影响行数
     */
    int insertSelective(GiftOrderEntity giftOrderEntity);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<GiftOrderEntity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<GiftOrderEntity> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<GiftOrderEntity> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<GiftOrderEntity> entities);

    /**
     * 修改数据
     *
     * @param giftOrderEntity 实例对象
     * @return 影响行数
     */
    int update(GiftOrderEntity giftOrderEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    // ========== 数据统计相关方法 ==========

    /**
     * 统计总订单数
     */
    Long countTotalOrders();

    /**
     * 统计总收入
     */
    Long getTotalRevenue();

    /**
     * 计算平均订单金额
     */
    Double getAvgOrderAmount();

    /**
     * 统计订单状态分布
     */
    List<StatItem> getOrderStatusStats();

    /**
     * 获取最近订单(5条)
     */
    List<RecentOrderVO> getRecentOrders();

}
