package com.mycom.system.mapper;

import com.mycom.system.domain.ServiceTypeEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-26 10:01:02
 */
public interface ServiceTypeMapper {

    /**
     * 条件查询
     *
     * @param serviceTypeEntity
     */
    List<ServiceTypeEntity> getList(ServiceTypeEntity serviceTypeEntity);

    /**
     * 通过ID查询
     *
     * @param id
     */
    ServiceTypeEntity selectByPrimaryKey(Long id);

    /**
     * 新增数据
     *
     * @param serviceTypeEntity
     */
    int insert(ServiceTypeEntity serviceTypeEntity);


    /**
     * 修改数据
     *
     * @param serviceTypeEntity 实例对象
     */
    int update(ServiceTypeEntity serviceTypeEntity);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     */
    int deleteById(Long id);

}

