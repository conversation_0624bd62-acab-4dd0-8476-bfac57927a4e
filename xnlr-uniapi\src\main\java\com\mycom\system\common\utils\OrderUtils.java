package com.mycom.system.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;

public class OrderUtils {
    // 业务前缀
    private static final String ORDER_PREFIX = "CPXN";

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    public static String generateOrderNumber() {
        // 获取当前时间戳（格式：yyyyMMddHHmmss）
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = dateFormat.format(new Date());

        // 生成随机数（4位随机数）
        int randomNum = ThreadLocalRandom.current().nextInt(1000, 10000);

        // 拼接订单号
        return ORDER_PREFIX + timestamp + randomNum;
    }

    /**
     * 生成时间
     *
     * @return 订单号
     */
    public static String generateTime() {
        // 获取当前时间戳（格式：yyyyMMddHHmmss）
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = dateFormat.format(new Date());
        return timestamp;
    }
}
