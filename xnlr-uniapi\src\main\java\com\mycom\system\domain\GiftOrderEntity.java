package com.mycom.system.domain;

import java.io.Serializable;

import lombok.Data;


/**
 * (GiftOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-02-25 18:51:27
 */


@Data

public class GiftOrderEntity implements Serializable {
    private static final long serialVersionUID = 724550985294149305L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 下单时间
     */
    private String paymentTime;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 买家id
     */
    private Long buyerId;

    /**
     * 卖家id
     */
    private Long sellerId;

    /**
     * 是否自定义金额打赏
     */
    private Boolean isReward;

    /**
     * 服务id
     */
    private Long serviceId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 总价值
     */
    private Integer totalPrice;

    /**
     * 微信号或者其他联系信息
     */
    private String wechat;

    /**
     * 给卖家的留言
     */
    private String message;


    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

}

