<template>
  <div
    :class="{ hidden: hidden }"
    class="pagination-container"
    v-if="total > 0"
  >
    <el-pagination
      :background="background"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="custom-pagination"
    />
  </div>
</template>

<script setup>
import { scrollTo } from "@/utils/scroll-to";

const props = defineProps({
  total: {
    required: true,
    type: Number,
  },
  page: {
    type: Number,
    default: 1,
  },
  limit: {
    type: Number,
    default: 20,
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50];
    },
  },
  // 移动端页码按钮的数量端默认值5
  pagerCount: {
    type: Number,
    default: document.body.clientWidth < 992 ? 5 : 7,
  },
  layout: {
    type: String,
    default: "prev, pager, next, sizes, jumper",
  },
  background: {
    type: Boolean,
    default: true,
  },
  autoScroll: {
    type: Boolean,
    default: true,
  },
  hidden: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits();
const currentPage = computed({
  get() {
    return props.page;
  },
  set(val) {
    emit("update:page", val);
  },
});
const pageSize = computed({
  get() {
    return props.limit;
  },
  set(val) {
    emit("update:limit", val);
  },
});

const totalPages = computed(() => {
  return Math.ceil(props.total / pageSize.value);
});

function handleSizeChange(val) {
  if (currentPage.value * val > props.total) {
    currentPage.value = 1;
  }
  emit("pagination", { page: currentPage.value, limit: val });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
}
function handleCurrentChange(val) {
  emit("pagination", { page: val, limit: pageSize.value });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
}
</script>

<style scoped>
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  margin-top: 20px;
}

.pagination-container.hidden {
  display: none;
}

/* 自定义分页器样式 */
:deep(.custom-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 8px;
  --el-pagination-button-color: #606266;
  --el-pagination-button-bg-color: #ffffff;
  --el-pagination-button-disabled-color: #c0c4cc;
  --el-pagination-button-disabled-bg-color: #ffffff;
  --el-pagination-hover-color: #409eff;
  --el-pagination-active-color: #409eff;
  background: #ffffff;
  border-radius: 12px;
  padding: 12px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

:deep(.custom-pagination .el-pager li) {
  margin: 0 3px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 36px;
  height: 36px;
  line-height: 34px;
}

:deep(.custom-pagination .el-pager li:hover) {
  color: #409eff;
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);
}

:deep(.custom-pagination .el-pager li.is-active) {
  color: #ffffff;
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

:deep(.custom-pagination .btn-prev),
:deep(.custom-pagination .btn-next) {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin: 0 3px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 36px;
  height: 36px;
}

:deep(.custom-pagination .btn-prev:hover),
:deep(.custom-pagination .btn-next:hover) {
  color: #409eff;
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);
}

:deep(.custom-pagination .el-pagination__jump) {
  margin-left: 16px;
}

:deep(.custom-pagination .el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.custom-pagination .el-input__wrapper:hover) {
  border-color: #409eff;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-container {
    padding: 16px 0;
  }

  :deep(.custom-pagination) {
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  :deep(.custom-pagination) {
    --el-pagination-font-size: 12px;
    padding: 6px 8px;
  }

  :deep(.custom-pagination .el-pager li),
  :deep(.custom-pagination .btn-prev),
  :deep(.custom-pagination .btn-next) {
    margin: 0 1px;
    min-width: 32px;
    height: 32px;
    line-height: 30px;
  }
}
</style>
