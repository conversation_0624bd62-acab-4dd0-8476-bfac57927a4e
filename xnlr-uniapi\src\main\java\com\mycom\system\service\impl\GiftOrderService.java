package com.mycom.system.service.impl;

import cn.hutool.db.sql.Order;
import com.mycom.system.common.exception.ServiceException;
import com.mycom.system.common.utils.OrderUtils;
import com.mycom.system.common.utils.SecurityUtils;
import com.mycom.system.common.utils.StringUtils;
import com.mycom.system.domain.*;
import com.mycom.system.mapper.GiftOrderMapper;
import org.apache.catalina.User;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * (GiftOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-25 18:51:27
 */
@Service("giftOrderService")
public class GiftOrderService {

    @Resource
    private GiftOrderMapper giftOrderMapper;

    @Resource
    private UserService userService;

    @Resource
    private LeaveMessageService leaveMessageService;

    @Resource
    private GiftReceiveService giftReceiveService;

    @Resource
    private GiftService giftService;

    @Resource
    private UserServiceService userServiceService;

    /**
     * 通过条件筛选，分页查询
     *
     * @param giftOrderEntity 查询条件
     * @return 多条数据
     */
    public PageInfo<OrderVo> getListPage(GiftOrderEntity giftOrderEntity) {
        PageHelper.startPage(giftOrderEntity.getPageNum(), giftOrderEntity.
                getPageSize());
        List<OrderVo> list = giftOrderMapper.getList(giftOrderEntity);
        return new PageInfo<>(list);
    }

    /**
     * 通过条件筛选，查询全部
     *
     * @param giftOrderEntity 查询条件
     * @return 多条数据
     */
    public List<OrderVo> getList(GiftOrderEntity giftOrderEntity) {
        List<OrderVo> list = giftOrderMapper.getList(giftOrderEntity);
        return list;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    public GiftOrderEntity queryById(Long id) {
        return giftOrderMapper.selectByPrimaryKey(id);
    }

    /**
     * 新增数据(两种订单：礼物/服务）
     *
     * @param giftOrderEntity 实例对象
     * @return 实例对象
     */
    @Transactional
    public GiftOrderEntity insert(GiftOrderEntity giftOrderEntity) {
        Long userId = SecurityUtils.getUserId();
        UserEntity userEntity = userService.queryById(userId);
        if (userEntity != null) {
            if (giftOrderEntity.getIsReward()) {
                giftOrderEntity.setQuantity(0);
            }
            // 新增打赏订单
            giftOrderEntity.setBuyerId(userId);
            giftOrderEntity.setOrderNumber(OrderUtils.generateOrderNumber());
            giftOrderEntity.setPaymentTime(OrderUtils.generateTime());
            giftOrderEntity.setStatus("已支付");
            giftOrderMapper.insert(giftOrderEntity);
            // 更新留言
            if (StringUtils.isNotEmpty(giftOrderEntity.getMessage())) {
                LeaveMessageEntity leaveMessage = new LeaveMessageEntity();
                leaveMessage.setUserId(giftOrderEntity.getSellerId());
                leaveMessage.setContent(giftOrderEntity.getMessage());
                leaveMessageService.insert(leaveMessage);
            }
            // 更新买家余额
            userEntity.setBalance(userEntity.getBalance() - giftOrderEntity.getTotalPrice());
            userService.update(userEntity);
        } else {
            throw new ServiceException("网络异常");
        }
        return giftOrderEntity;
    }

    /**
     * 修改数据
     *
     * @param giftOrderEntity 实例对象
     * @return 实例对象
     */
    public GiftOrderEntity update(GiftOrderEntity giftOrderEntity) {
        giftOrderMapper.update(giftOrderEntity);
        return queryById(giftOrderEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public boolean deleteById(Long id) {
        return giftOrderMapper.deleteById(id) > 0;
    }
}

