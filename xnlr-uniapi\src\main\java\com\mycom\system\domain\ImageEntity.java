package com.mycom.system.domain;

import java.io.Serializable;

import lombok.Data;


/**
 * (Image)实体类
 *
 * <AUTHOR>
 * @since 2025-02-26 15:41:04
 */


@Data

public class ImageEntity implements Serializable {
    private static final long serialVersionUID = -53187173438761041L;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 图片网址
     */
    private String url;


    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

}

