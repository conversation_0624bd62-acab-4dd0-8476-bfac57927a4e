package com.mycom.system.service.impl;

import com.mycom.system.domain.UserServiceEntity;
import com.mycom.system.mapper.UserServiceMapper;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import javax.annotation.Resource;
import java.util.List;

/**
 * (Service)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-23 14:38:54
 */
@Service("serviceService")
public class UserServiceService {
    @Resource
    private UserServiceMapper serviceMapper;

    /**
     * 通过条件筛选，分页查询
     *
     * @param userServiceEntity 查询条件
     * @return 多条数据
     */
    public PageInfo<UserServiceEntity> getListPage(UserServiceEntity userServiceEntity) {
        PageHelper.startPage(userServiceEntity.getPageNum(), userServiceEntity.
                getPageSize());
        List<UserServiceEntity> list = serviceMapper.getList(userServiceEntity);
        return new PageInfo<>(list);
    }

    /**
     * 通过条件筛选，
     *
     * @param userServiceEntity 查询条件
     * @return 多条数据
     */
    public List<UserServiceEntity> getList(UserServiceEntity userServiceEntity) {
        List<UserServiceEntity> list = serviceMapper.getList(userServiceEntity);
        return list;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    public UserServiceEntity queryById(Long id) {
        return serviceMapper.selectByPrimaryKey(id);
    }

    /**
     * 新增数据
     *
     * @param userServiceEntity 实例对象
     * @return 实例对象
     */
    public UserServiceEntity insert(UserServiceEntity userServiceEntity) {
        serviceMapper.insert(userServiceEntity);
        return userServiceEntity;
    }

    /**
     * 修改数据
     *
     * @param userServiceEntity 实例对象
     * @return 实例对象
     */
    public UserServiceEntity update(UserServiceEntity userServiceEntity) {
        serviceMapper.update(userServiceEntity);
        return queryById(userServiceEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    public boolean deleteById(Long id) {
        return serviceMapper.deleteById(id) > 0;
    }


}

