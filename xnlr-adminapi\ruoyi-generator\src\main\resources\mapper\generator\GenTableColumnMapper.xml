<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.generator.mapper.GenTableColumnMapper">

    <resultMap type="GenTableColumn" id="GenTableColumnResult">
        <id     property="columnId"       column="column_id"      />
        <result property="tableId"        column="table_id"       />
        <result property="columnName"     column="column_name"    />
        <result property="columnComment"  column="column_comment" />
        <result property="columnType"     column="column_type"    />
        <result property="javaType"       column="java_type"      />
        <result property="javaField"      column="java_field"     />
        <result property="isPk"           column="is_pk"          />
        <result property="isIncrement"    column="is_increment"   />
        <result property="isRequired"     column="is_required"    />
        <result property="isInsert"       column="is_insert"      />
        <result property="isEdit"         column="is_edit"        />
        <result property="isList"         column="is_list"        />
        <result property="isQuery"        column="is_query"       />
        <result property="queryType"      column="query_type"     />
        <result property="htmlType"       column="html_type"      />
        <result property="dictType"       column="dict_type"      />
        <result property="sort"           column="sort"           />
        <result property="createBy"       column="create_by"      />
        <result property="createTime"     column="create_time"    />
        <result property="updateBy"       column="update_by"      />
        <result property="updateTime"     column="update_time"    />
    </resultMap>

	<sql id="selectGenTableColumnVo">
        select column_id, table_id, column_name, column_comment, column_type, java_type, java_field, is_pk, is_increment, is_required, is_insert, is_edit, is_list, is_query, query_type, html_type, dict_type, sort, create_by, create_time, update_by, update_time from gen_table_column
    </sql>

    <select id="selectGenTableColumnListByTableId" parameterType="Long" resultMap="GenTableColumnResult">
        <include refid="selectGenTableColumnVo"/>
        where table_id = #{tableId}
        order by sort
    </select>

    <select id="selectDbTableColumnsByName" parameterType="String" resultMap="GenTableColumnResult">
		select column_name, (case when (is_nullable = 'no' <![CDATA[ && ]]> column_key != 'PRI') then '1' else '0' end) as is_required, (case when column_key = 'PRI' then '1' else '0' end) as is_pk, ordinal_position as sort, column_comment, (case when extra = 'auto_increment' then '1' else '0' end) as is_increment, column_type
		from information_schema.columns where table_schema = (select database()) and table_name = (#{tableName})
		order by ordinal_position
	</select>

    <insert id="insertGenTableColumn" parameterType="GenTableColumn" useGeneratedKeys="true" keyProperty="columnId">
        insert into gen_table_column (
			<if test="tableId != null and tableId != ''">table_id,</if>
			<if test="columnName != null and columnName != ''">column_name,</if>
			<if test="columnComment != null and columnComment != ''">column_comment,</if>
			<if test="columnType != null and columnType != ''">column_type,</if>
			<if test="javaType != null and javaType != ''">java_type,</if>
			<if test="javaField != null  and javaField != ''">java_field,</if>
			<if test="isPk != null and isPk != ''">is_pk,</if>
			<if test="isIncrement != null and isIncrement != ''">is_increment,</if>
			<if test="isRequired != null and isRequired != ''">is_required,</if>
			<if test="isInsert != null and isInsert != ''">is_insert,</if>
			<if test="isEdit != null and isEdit != ''">is_edit,</if>
			<if test="isList != null and isList != ''">is_list,</if>
			<if test="isQuery != null and isQuery != ''">is_query,</if>
			<if test="queryType != null and queryType != ''">query_type,</if>
			<if test="htmlType != null and htmlType != ''">html_type,</if>
			<if test="dictType != null and dictType != ''">dict_type,</if>
			<if test="sort != null">sort,</if>
			<if test="createBy != null and createBy != ''">create_by,</if>
			create_time
         )values(
			<if test="tableId != null and tableId != ''">#{tableId},</if>
			<if test="columnName != null and columnName != ''">#{columnName},</if>
			<if test="columnComment != null and columnComment != ''">#{columnComment},</if>
			<if test="columnType != null and columnType != ''">#{columnType},</if>
			<if test="javaType != null and javaType != ''">#{javaType},</if>
			<if test="javaField != null and javaField != ''">#{javaField},</if>
			<if test="isPk != null and isPk != ''">#{isPk},</if>
			<if test="isIncrement != null and isIncrement != ''">#{isIncrement},</if>
			<if test="isRequired != null and isRequired != ''">#{isRequired},</if>
			<if test="isInsert != null and isInsert != ''">#{isInsert},</if>
			<if test="isEdit != null and isEdit != ''">#{isEdit},</if>
			<if test="isList != null and isList != ''">#{isList},</if>
			<if test="isQuery != null and isQuery != ''">#{isQuery},</if>
			<if test="queryType != null and queryType != ''">#{queryType},</if>
			<if test="htmlType != null and htmlType != ''">#{htmlType},</if>
			<if test="dictType != null and dictType != ''">#{dictType},</if>
			<if test="sort != null">#{sort},</if>
			<if test="createBy != null and createBy != ''">#{createBy},</if>
			sysdate()
         )
    </insert>

    <update id="updateGenTableColumn" parameterType="GenTableColumn">
        update gen_table_column
        <set>
            <if test="columnComment != null">column_comment = #{columnComment},</if>
            <if test="javaType != null">java_type = #{javaType},</if>
            <if test="javaField != null">java_field = #{javaField},</if>
            <if test="isInsert != null">is_insert = #{isInsert},</if>
            <if test="isEdit != null">is_edit = #{isEdit},</if>
            <if test="isList != null">is_list = #{isList},</if>
            <if test="isQuery != null">is_query = #{isQuery},</if>
            <if test="isRequired != null">is_required = #{isRequired},</if>
            <if test="queryType != null">query_type = #{queryType},</if>
            <if test="htmlType != null">html_type = #{htmlType},</if>
            <if test="dictType != null">dict_type = #{dictType},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where column_id = #{columnId}
    </update>

    <delete id="deleteGenTableColumnByIds" parameterType="Long">
        delete from gen_table_column where table_id in
        <foreach collection="array" item="tableId" open="(" separator="," close=")">
            #{tableId}
        </foreach>
    </delete>

    <delete id="deleteGenTableColumns">
        delete from gen_table_column where column_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.columnId}
        </foreach>
    </delete>

</mapper>